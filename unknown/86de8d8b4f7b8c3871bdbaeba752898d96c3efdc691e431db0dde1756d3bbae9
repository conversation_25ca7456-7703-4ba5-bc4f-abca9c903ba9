"""
Category endpoints for the Daily Motivator API.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas.category import CategoryCreate, CategoryUpdate, CategoryResponse
from app.services.category_service import CategoryService
from app.api.deps import get_current_active_user, get_current_superuser
from app.models.user import User

router = APIRouter()


@router.get("/", response_model=List[CategoryResponse])
async def get_categories(
    skip: int = Query(0, ge=0, description="Number of categories to skip"),
    limit: int = Query(100, ge=1, le=100, description="Number of categories to return"),
    search: Optional[str] = Query(None, description="Search categories by name or description"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Session = Depends(get_db)
):
    """Get list of categories."""
    category_service = CategoryService(db)
    categories = category_service.get_categories(
        skip=skip,
        limit=limit,
        search=search,
        is_active=is_active
    )
    return categories


@router.get("/active", response_model=List[CategoryResponse])
async def get_active_categories(db: Session = Depends(get_db)):
    """Get all active categories."""
    category_service = CategoryService(db)
    return category_service.get_active_categories()


@router.get("/popular", response_model=List[CategoryResponse])
async def get_popular_categories(
    limit: int = Query(10, ge=1, le=50, description="Number of popular categories to return"),
    db: Session = Depends(get_db)
):
    """Get popular categories."""
    category_service = CategoryService(db)
    return category_service.get_popular_categories(limit=limit)


@router.get("/{category_id}", response_model=CategoryResponse)
async def get_category(
    category_id: int,
    db: Session = Depends(get_db)
):
    """Get category by ID."""
    category_service = CategoryService(db)
    category = category_service.get_category_by_id(category_id)
    
    if not category:
        raise HTTPException(status_code=404, detail="Category not found")
    
    return category


@router.post("/", response_model=CategoryResponse)
async def create_category(
    category_data: CategoryCreate,
    current_user: User = Depends(get_current_superuser),
    db: Session = Depends(get_db)
):
    """Create a new category (admin only)."""
    category_service = CategoryService(db)
    return category_service.create_category(category_data)


@router.put("/{category_id}", response_model=CategoryResponse)
async def update_category(
    category_id: int,
    category_data: CategoryUpdate,
    current_user: User = Depends(get_current_superuser),
    db: Session = Depends(get_db)
):
    """Update category (admin only)."""
    category_service = CategoryService(db)
    category = category_service.update_category(category_id, category_data)
    
    if not category:
        raise HTTPException(status_code=404, detail="Category not found")
    
    return category


@router.delete("/{category_id}")
async def delete_category(
    category_id: int,
    current_user: User = Depends(get_current_superuser),
    db: Session = Depends(get_db)
):
    """Delete category (admin only)."""
    category_service = CategoryService(db)
    success = category_service.delete_category(category_id)
    
    if not success:
        raise HTTPException(status_code=404, detail="Category not found or has associated quotes")
    
    return {"message": "Category deleted successfully"}


@router.post("/{category_id}/activate")
async def activate_category(
    category_id: int,
    current_user: User = Depends(get_current_superuser),
    db: Session = Depends(get_db)
):
    """Activate category (admin only)."""
    category_service = CategoryService(db)
    success = category_service.activate_category(category_id)
    
    if not success:
        raise HTTPException(status_code=404, detail="Category not found")
    
    return {"message": "Category activated successfully"}


@router.post("/{category_id}/deactivate")
async def deactivate_category(
    category_id: int,
    current_user: User = Depends(get_current_superuser),
    db: Session = Depends(get_db)
):
    """Deactivate category (admin only)."""
    category_service = CategoryService(db)
    success = category_service.deactivate_category(category_id)
    
    if not success:
        raise HTTPException(status_code=404, detail="Category not found")
    
    return {"message": "Category deactivated successfully"}
