#!/usr/bin/env python3
"""
Migration script to set up PostgreSQL database for Render.com deployment.
This script creates all necessary tables and initial data.
"""

import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the path so we can import our app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.core.config import get_database_url, settings
from app.core.database import Base
from app.models import user, quote, category, user_preference, analytics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_database_if_not_exists():
    """Create database if it doesn't exist (for local development)."""
    try:
        # For Render.com, the database is already created
        if os.getenv('RENDER'):
            logger.info("Running on Render.com, skipping database creation")
            return
        
        # For local PostgreSQL setup
        if settings.POSTGRES_SERVER:
            # Connect to postgres database to create our app database
            admin_url = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/postgres"
            admin_engine = create_engine(admin_url)
            
            with admin_engine.connect() as conn:
                # Check if database exists
                result = conn.execute(text(f"SELECT 1 FROM pg_database WHERE datname = '{settings.POSTGRES_DB}'"))
                if not result.fetchone():
                    # Create database
                    conn.execute(text("COMMIT"))  # End any existing transaction
                    conn.execute(text(f"CREATE DATABASE {settings.POSTGRES_DB}"))
                    logger.info(f"Created database: {settings.POSTGRES_DB}")
                else:
                    logger.info(f"Database {settings.POSTGRES_DB} already exists")
            
            admin_engine.dispose()
    
    except Exception as e:
        logger.error(f"Error creating database: {e}")
        # Continue anyway, database might already exist


def migrate_database():
    """Run database migration."""
    try:
        logger.info("Starting database migration...")
        
        # Create database if needed
        create_database_if_not_exists()
        
        # Get database URL
        db_url = get_database_url()
        logger.info(f"Connecting to database: {db_url.split('@')[0]}@***")
        
        # Create engine
        engine = create_engine(db_url)
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            logger.info(f"Connected to PostgreSQL: {version}")
        
        # Create all tables
        logger.info("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        
        # Seed initial data
        seed_initial_data(engine)
        
        logger.info("Database migration completed successfully")
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise


def seed_initial_data(engine):
    """Seed initial data into the database."""
    from sqlalchemy.orm import sessionmaker
    from app.models.category import Category
    from app.models.quote import Quote
    
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Check if categories already exist
        if session.query(Category).count() > 0:
            logger.info("Categories already exist, skipping seed data")
            return
        
        logger.info("Seeding initial data...")
        
        # Create categories
        categories_data = [
            {"name": "Motivation", "slug": "motivation", "description": "Motivational quotes to inspire action", "color": "#FF6B6B", "icon": "🚀"},
            {"name": "Success", "slug": "success", "description": "Quotes about achieving success", "color": "#4ECDC4", "icon": "🏆"},
            {"name": "Inspiration", "slug": "inspiration", "description": "Inspirational quotes for daily life", "color": "#45B7D1", "icon": "✨"},
            {"name": "Wisdom", "slug": "wisdom", "description": "Wise words from great minds", "color": "#96CEB4", "icon": "🧠"},
            {"name": "Courage", "slug": "courage", "description": "Quotes about bravery and courage", "color": "#FFEAA7", "icon": "💪"},
            {"name": "Love", "slug": "love", "description": "Quotes about love and relationships", "color": "#FD79A8", "icon": "❤️"},
            {"name": "Happiness", "slug": "happiness", "description": "Quotes to bring joy and happiness", "color": "#FDCB6E", "icon": "😊"},
            {"name": "Leadership", "slug": "leadership", "description": "Leadership and management quotes", "color": "#6C5CE7", "icon": "👑"},
        ]
        
        categories = []
        for i, cat_data in enumerate(categories_data):
            category = Category(
                **cat_data,
                sort_order=i,
                is_active=True
            )
            categories.append(category)
            session.add(category)
        
        session.commit()
        logger.info(f"Created {len(categories)} categories")
        
        # Create sample quotes
        quotes_data = [
            {"text": "The only way to do great work is to love what you do.", "author": "Steve Jobs", "category_slug": "motivation"},
            {"text": "Success is not final, failure is not fatal: it is the courage to continue that counts.", "author": "Winston Churchill", "category_slug": "success"},
            {"text": "The future belongs to those who believe in the beauty of their dreams.", "author": "Eleanor Roosevelt", "category_slug": "inspiration"},
            {"text": "It is during our darkest moments that we must focus to see the light.", "author": "Aristotle", "category_slug": "wisdom"},
            {"text": "Courage is not the absence of fear, but action in spite of it.", "author": "Mark Twain", "category_slug": "courage"},
            {"text": "Love yourself first and everything else falls into line.", "author": "Lucille Ball", "category_slug": "love"},
            {"text": "Happiness is not something ready made. It comes from your own actions.", "author": "Dalai Lama", "category_slug": "happiness"},
            {"text": "A leader is one who knows the way, goes the way, and shows the way.", "author": "John C. Maxwell", "category_slug": "leadership"},
        ]
        
        # Get category mapping
        category_map = {cat.slug: cat.id for cat in categories}
        
        for quote_data in quotes_data:
            category_id = category_map.get(quote_data["category_slug"])
            if category_id:
                quote = Quote(
                    text=quote_data["text"],
                    author=quote_data["author"],
                    category_id=category_id,
                    is_approved=True,
                    is_featured=True
                )
                session.add(quote)
        
        session.commit()
        logger.info(f"Created {len(quotes_data)} sample quotes")
        
    except Exception as e:
        session.rollback()
        logger.error(f"Error seeding data: {e}")
        raise
    finally:
        session.close()


if __name__ == "__main__":
    migrate_database()
