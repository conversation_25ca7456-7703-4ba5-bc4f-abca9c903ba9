#!/usr/bin/env python3
"""
Database initialization script for Daily Motivator API.
Creates tables and populates with sample data.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import app modules
sys.path.append(str(Path(__file__).parent.parent))

from app.core.database import init_db, get_db, SessionLocal
from app.core.security import get_password_hash
from app.models.user import User
from app.models.category import Category
from app.models.quote import Quote
from app.models.user_preference import UserPreference


def create_sample_categories():
    """Create sample categories."""
    categories = [
        {
            "name": "Motivation",
            "slug": "motivation",
            "description": "Inspiring quotes to motivate and energize you",
            "color": "#FF6B6B",
            "icon": "🔥",
            "sort_order": 1
        },
        {
            "name": "Success",
            "slug": "success",
            "description": "Quotes about achieving success and reaching goals",
            "color": "#4ECDC4",
            "icon": "🏆",
            "sort_order": 2
        },
        {
            "name": "Wisdom",
            "slug": "wisdom",
            "description": "Timeless wisdom and life lessons",
            "color": "#45B7D1",
            "icon": "🧠",
            "sort_order": 3
        },
        {
            "name": "Happiness",
            "slug": "happiness",
            "description": "Quotes to bring joy and positivity to your day",
            "color": "#F9CA24",
            "icon": "😊",
            "sort_order": 4
        },
        {
            "name": "Perseverance",
            "slug": "perseverance",
            "description": "Quotes about persistence and never giving up",
            "color": "#6C5CE7",
            "icon": "💪",
            "sort_order": 5
        },
        {
            "name": "Leadership",
            "slug": "leadership",
            "description": "Inspiring quotes about leadership and influence",
            "color": "#A29BFE",
            "icon": "👑",
            "sort_order": 6
        },
        {
            "name": "Life",
            "slug": "life",
            "description": "Quotes about life, purpose, and meaning",
            "color": "#FD79A8",
            "icon": "🌱",
            "sort_order": 7
        },
        {
            "name": "Dreams",
            "slug": "dreams",
            "description": "Quotes about following your dreams and aspirations",
            "color": "#FDCB6E",
            "icon": "✨",
            "sort_order": 8
        }
    ]
    
    db = SessionLocal()
    try:
        for cat_data in categories:
            category = Category(**cat_data)
            db.add(category)
        
        db.commit()
        print(f"Created {len(categories)} categories")
    
    except Exception as e:
        print(f"Error creating categories: {e}")
        db.rollback()
    
    finally:
        db.close()


def create_sample_quotes():
    """Create sample quotes."""
    quotes = [
        {
            "text": "The only way to do great work is to love what you do.",
            "author": "Steve Jobs",
            "category_id": 1,  # Motivation
            "tags": "work, passion, greatness",
            "is_featured": True
        },
        {
            "text": "Success is not final, failure is not fatal: it is the courage to continue that counts.",
            "author": "Winston Churchill",
            "category_id": 2,  # Success
            "tags": "success, failure, courage, persistence",
            "is_featured": True
        },
        {
            "text": "The only true wisdom is in knowing you know nothing.",
            "author": "Socrates",
            "category_id": 3,  # Wisdom
            "tags": "wisdom, knowledge, humility"
        },
        {
            "text": "Happiness is not something ready made. It comes from your own actions.",
            "author": "Dalai Lama",
            "category_id": 4,  # Happiness
            "tags": "happiness, actions, self-made"
        },
        {
            "text": "It does not matter how slowly you go as long as you do not stop.",
            "author": "Confucius",
            "category_id": 5,  # Perseverance
            "tags": "perseverance, progress, persistence"
        },
        {
            "text": "A leader is one who knows the way, goes the way, and shows the way.",
            "author": "John C. Maxwell",
            "category_id": 6,  # Leadership
            "tags": "leadership, guidance, example"
        },
        {
            "text": "Life is what happens to you while you're busy making other plans.",
            "author": "John Lennon",
            "category_id": 7,  # Life
            "tags": "life, plans, present moment"
        },
        {
            "text": "All our dreams can come true, if we have the courage to pursue them.",
            "author": "Walt Disney",
            "category_id": 8,  # Dreams
            "tags": "dreams, courage, pursuit",
            "is_featured": True
        },
        {
            "text": "The future belongs to those who believe in the beauty of their dreams.",
            "author": "Eleanor Roosevelt",
            "category_id": 8,  # Dreams
            "tags": "future, dreams, belief"
        },
        {
            "text": "Don't watch the clock; do what it does. Keep going.",
            "author": "Sam Levenson",
            "category_id": 5,  # Perseverance
            "tags": "time, persistence, action"
        },
        {
            "text": "The best time to plant a tree was 20 years ago. The second best time is now.",
            "author": "Chinese Proverb",
            "category_id": 1,  # Motivation
            "tags": "action, timing, opportunity"
        },
        {
            "text": "Your limitation—it's only your imagination.",
            "author": "Unknown",
            "category_id": 1,  # Motivation
            "tags": "limitations, imagination, mindset"
        },
        {
            "text": "Great things never come from comfort zones.",
            "author": "Unknown",
            "category_id": 2,  # Success
            "tags": "comfort zone, growth, achievement"
        },
        {
            "text": "Dream it. Wish it. Do it.",
            "author": "Unknown",
            "category_id": 8,  # Dreams
            "tags": "dreams, action, achievement"
        },
        {
            "text": "Success doesn't just find you. You have to go out and get it.",
            "author": "Unknown",
            "category_id": 2,  # Success
            "tags": "success, action, effort"
        },
        {
            "text": "The harder you work for something, the greater you'll feel when you achieve it.",
            "author": "Unknown",
            "category_id": 2,  # Success
            "tags": "hard work, achievement, satisfaction"
        },
        {
            "text": "Dream bigger. Do bigger.",
            "author": "Unknown",
            "category_id": 8,  # Dreams
            "tags": "dreams, ambition, action"
        },
        {
            "text": "Don't stop when you're tired. Stop when you're done.",
            "author": "Unknown",
            "category_id": 5,  # Perseverance
            "tags": "persistence, completion, endurance"
        },
        {
            "text": "Wake up with determination. Go to bed with satisfaction.",
            "author": "Unknown",
            "category_id": 1,  # Motivation
            "tags": "determination, satisfaction, daily routine"
        },
        {
            "text": "Do something today that your future self will thank you for.",
            "author": "Sean Patrick Flanery",
            "category_id": 7,  # Life
            "tags": "future, action, gratitude"
        }
    ]
    
    db = SessionLocal()
    try:
        for quote_data in quotes:
            quote = Quote(**quote_data)
            db.add(quote)
        
        db.commit()
        print(f"Created {len(quotes)} quotes")
    
    except Exception as e:
        print(f"Error creating quotes: {e}")
        db.rollback()
    
    finally:
        db.close()


def create_sample_users():
    """Create sample users."""
    users = [
        {
            "email": "<EMAIL>",
            "username": "admin",
            "hashed_password": get_password_hash("admin123"),
            "full_name": "Admin User",
            "is_superuser": True,
            "is_verified": True
        },
        {
            "email": "<EMAIL>",
            "username": "demo",
            "hashed_password": get_password_hash("demo123"),
            "full_name": "Demo User",
            "is_verified": True
        },
        {
            "email": "<EMAIL>",
            "username": "john_doe",
            "hashed_password": get_password_hash("password123"),
            "full_name": "John Doe",
            "bio": "Motivation enthusiast and daily quote reader",
            "is_verified": True,
            "points": 150,
            "level": 2,
            "streak_count": 7
        }
    ]
    
    db = SessionLocal()
    try:
        for user_data in users:
            user = User(**user_data)
            db.add(user)
        
        db.commit()
        print(f"Created {len(users)} users")
    
    except Exception as e:
        print(f"Error creating users: {e}")
        db.rollback()
    
    finally:
        db.close()


def main():
    """Main initialization function."""
    print("Initializing Daily Motivator database...")
    
    try:
        # Initialize database tables
        print("Creating database tables...")
        init_db()
        print("Database tables created successfully")
        
        # Create sample data
        print("Creating sample data...")
        create_sample_categories()
        create_sample_quotes()
        create_sample_users()
        
        print("\n✅ Database initialization completed successfully!")
        print("\nSample users created:")
        print("- Admin: <EMAIL> / admin123")
        print("- Demo: <EMAIL> / demo123")
        print("- John: <EMAIL> / password123")
        
    except Exception as e:
        print(f"\n❌ Database initialization failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
