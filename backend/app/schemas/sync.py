"""
Schemas for sync operations between mobile app and server.
Handles offline/online synchronization data structures.
"""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


class SyncRequest(BaseModel):
    """Request for syncing data from mobile app."""
    last_sync_timestamp: Optional[str] = Field(None, description="ISO timestamp of last sync")
    category_ids: Optional[List[int]] = Field(None, description="Filter by category IDs")
    limit: Optional[int] = Field(100, ge=1, le=500, description="Maximum number of items to sync")
    include_featured_only: bool = Field(False, description="Include only featured content")


class QuoteSync(BaseModel):
    """Quote data for synchronization."""
    id: int
    text: str
    author: str
    category_id: int
    category_name: str
    tags: List[str] = []
    is_featured: bool = False
    quality_score: float = 0.0
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True


class CategorySync(BaseModel):
    """Category data for synchronization."""
    id: int
    name: str
    slug: str
    description: Optional[str] = None
    color: str = "#4ECDC4"
    icon: str = "📝"
    quote_count: int = 0
    sort_order: int = 0
    updated_at: str
    
    class Config:
        from_attributes = True


class SyncResponse(BaseModel):
    """Response for sync operations."""
    quotes: List[QuoteSync]
    total_count: int
    sync_timestamp: str
    has_more: bool = False


class OfflineQuoteRequest(BaseModel):
    """Request for offline quotes."""
    categories: Optional[List[int]] = None
    count: int = Field(50, ge=1, le=200)
    featured_only: bool = False


class WidgetQuoteResponse(BaseModel):
    """Response for Android widget quote."""
    id: int
    text: str
    author: str
    category: str
    category_color: str = "#4ECDC4"
    
    class Config:
        from_attributes = True


class AnalyticsEvent(BaseModel):
    """Analytics event for offline sync."""
    event_type: str = Field(..., description="Type of event (view, share, favorite, etc.)")
    quote_id: Optional[int] = None
    category_id: Optional[int] = None
    timestamp: str = Field(..., description="ISO timestamp when event occurred")
    metadata: Optional[dict] = Field(None, description="Additional event data")


class AnalyticsBatch(BaseModel):
    """Batch of analytics events for sync."""
    events: List[AnalyticsEvent]
    device_id: Optional[str] = None
    app_version: Optional[str] = None


class SyncStatus(BaseModel):
    """Status of sync operation."""
    last_sync: Optional[str] = None
    quotes_count: int = 0
    categories_count: int = 0
    pending_analytics: int = 0
    is_online: bool = True


class OfflineConfig(BaseModel):
    """Configuration for offline mode."""
    max_quotes_cache: int = Field(500, description="Maximum quotes to cache offline")
    sync_interval_hours: int = Field(24, description="Hours between automatic syncs")
    auto_sync_on_wifi: bool = Field(True, description="Auto sync when on WiFi")
    preload_featured: bool = Field(True, description="Preload featured quotes")
    categories_to_sync: Optional[List[int]] = Field(None, description="Specific categories to sync")


class WidgetConfig(BaseModel):
    """Configuration for Android widget."""
    refresh_interval_minutes: int = Field(60, description="Minutes between widget refreshes")
    category_filter: Optional[str] = Field(None, description="Category slug to filter by")
    show_author: bool = Field(True, description="Show author name in widget")
    background_color: str = Field("#FFFFFF", description="Widget background color")
    text_color: str = Field("#000000", description="Widget text color")
    use_api: bool = Field(True, description="Use API for new quotes vs offline only")


class AppConfig(BaseModel):
    """App configuration for sync."""
    offline_config: OfflineConfig
    widget_config: WidgetConfig
    api_base_url: str = Field("https://your-app.onrender.com/api/v1", description="Base API URL")
    enable_analytics: bool = Field(True, description="Enable analytics collection")
    guest_mode: bool = Field(True, description="Allow guest access without login")
