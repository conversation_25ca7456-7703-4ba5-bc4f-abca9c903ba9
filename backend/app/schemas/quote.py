"""
Quote schemas for request/response validation.
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, validator


class QuoteBase(BaseModel):
    """Base quote schema with common fields."""
    text: str
    author: str
    source: Optional[str] = None
    category_id: int
    tags: Optional[List[str]] = None
    language: str = "en"
    source_url: Optional[str] = None
    attribution_required: bool = False
    copyright_info: Optional[str] = None
    
    @validator("text")
    def validate_text(cls, v):
        if len(v.strip()) < 10:
            raise ValueError("Quote text must be at least 10 characters long")
        if len(v) > 2000:
            raise ValueError("Quote text must be less than 2000 characters")
        return v.strip()
    
    @validator("author")
    def validate_author(cls, v):
        if len(v.strip()) < 2:
            raise ValueError("Author name must be at least 2 characters long")
        if len(v) > 255:
            raise ValueError("Author name must be less than 255 characters")
        return v.strip()
    
    @validator("language")
    def validate_language(cls, v):
        # Simple language code validation
        if len(v) not in [2, 5]:  # 'en' or 'en-US'
            raise ValueError("Language must be a valid language code")
        return v.lower()


class QuoteCreate(QuoteBase):
    """Schema for creating a new quote."""
    pass


class QuoteUpdate(BaseModel):
    """Schema for updating quote information."""
    text: Optional[str] = None
    author: Optional[str] = None
    source: Optional[str] = None
    category_id: Optional[int] = None
    tags: Optional[List[str]] = None
    language: Optional[str] = None
    source_url: Optional[str] = None
    attribution_required: Optional[bool] = None
    copyright_info: Optional[str] = None
    is_approved: Optional[bool] = None
    is_featured: Optional[bool] = None
    
    @validator("text")
    def validate_text(cls, v):
        if v is not None:
            if len(v.strip()) < 10:
                raise ValueError("Quote text must be at least 10 characters long")
            if len(v) > 2000:
                raise ValueError("Quote text must be less than 2000 characters")
            return v.strip()
        return v
    
    @validator("author")
    def validate_author(cls, v):
        if v is not None:
            if len(v.strip()) < 2:
                raise ValueError("Author name must be at least 2 characters long")
            if len(v) > 255:
                raise ValueError("Author name must be less than 255 characters")
            return v.strip()
        return v


class QuoteResponse(BaseModel):
    """Schema for quote response."""
    id: int
    text: str
    author: str
    source: Optional[str]
    category_id: int
    tags: List[str]
    language: str
    length: int
    word_count: int
    reading_time: int
    is_approved: bool
    is_featured: bool
    quality_score: float
    source_url: Optional[str]
    attribution_required: bool
    copyright_info: Optional[str]
    created_at: datetime
    updated_at: datetime
    last_shown: Optional[datetime]

    @validator('tags', pre=True)
    def parse_tags(cls, v):
        if isinstance(v, str):
            return [tag.strip() for tag in v.split(',') if tag.strip()] if v else []
        return v or []

    class Config:
        from_attributes = True


class QuoteWithStats(QuoteResponse):
    """Schema for quote response with engagement statistics."""
    view_count: int
    like_count: int
    share_count: int
    favorite_count: int
    engagement_rate: float
    
    class Config:
        from_attributes = True


class QuoteSearch(BaseModel):
    """Schema for quote search parameters."""
    query: Optional[str] = None
    author: Optional[str] = None
    category_id: Optional[int] = None
    tags: Optional[List[str]] = None
    language: Optional[str] = None
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    is_featured: Optional[bool] = None
    sort_by: str = "created_at"
    sort_order: str = "desc"
    page: int = 1
    page_size: int = 20
    
    @validator("sort_by")
    def validate_sort_by(cls, v):
        allowed_fields = [
            "created_at", "updated_at", "author", "quality_score",
            "view_count", "like_count", "share_count", "favorite_count"
        ]
        if v not in allowed_fields:
            raise ValueError(f"sort_by must be one of: {', '.join(allowed_fields)}")
        return v
    
    @validator("sort_order")
    def validate_sort_order(cls, v):
        if v.lower() not in ["asc", "desc"]:
            raise ValueError("sort_order must be 'asc' or 'desc'")
        return v.lower()
    
    @validator("page")
    def validate_page(cls, v):
        if v < 1:
            raise ValueError("page must be greater than 0")
        return v
    
    @validator("page_size")
    def validate_page_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError("page_size must be between 1 and 100")
        return v


class QuoteInteraction(BaseModel):
    """Schema for quote interaction."""
    interaction_type: str
    value: float = 1.0
    platform: Optional[str] = None
    source: Optional[str] = None
    time_spent: Optional[int] = None
    scroll_depth: Optional[float] = None
    
    @validator("interaction_type")
    def validate_interaction_type(cls, v):
        allowed_types = ["view", "like", "unlike", "share", "favorite", "unfavorite", "copy"]
        if v not in allowed_types:
            raise ValueError(f"interaction_type must be one of: {', '.join(allowed_types)}")
        return v
    
    @validator("scroll_depth")
    def validate_scroll_depth(cls, v):
        if v is not None and (v < 0 or v > 100):
            raise ValueError("scroll_depth must be between 0 and 100")
        return v


class QuoteList(BaseModel):
    """Schema for paginated quote list."""
    quotes: List[QuoteResponse]
    total: int
    page: int
    page_size: int
    total_pages: int
    
    class Config:
        from_attributes = True


class RandomQuoteRequest(BaseModel):
    """Schema for random quote request."""
    category_id: Optional[int] = None
    exclude_recent: bool = True
    user_preferences: bool = True
