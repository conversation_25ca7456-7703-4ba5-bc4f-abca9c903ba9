"""
User preference schemas for request/response validation.
"""

from datetime import datetime, time
from typing import Optional, List
from pydantic import BaseModel, validator


class UserPreferenceBase(BaseModel):
    """Base user preference schema with common fields."""
    notification_enabled: bool = True
    notification_frequency: str = "daily"
    notification_time: time = time(9, 0)
    notification_days: List[int] = [1, 2, 3, 4, 5, 6, 7]
    preferred_quote_length: str = "any"
    preferred_authors: Optional[List[str]] = None
    excluded_authors: Optional[List[str]] = None
    preferred_tags: Optional[List[str]] = None
    font_size: str = "medium"
    theme_preference: str = "system"
    background_type: str = "image"
    background_value: Optional[str] = None
    gamification_enabled: bool = True
    streak_notifications: bool = True
    achievement_notifications: bool = True
    profile_public: bool = False
    share_statistics: bool = True
    allow_friend_requests: bool = True
    show_author: bool = True
    show_source: bool = True
    auto_mark_read: bool = True
    
    @validator("notification_frequency")
    def validate_notification_frequency(cls, v):
        allowed_frequencies = ["daily", "weekly", "custom", "never"]
        if v not in allowed_frequencies:
            raise ValueError(f"notification_frequency must be one of: {', '.join(allowed_frequencies)}")
        return v
    
    @validator("notification_days")
    def validate_notification_days(cls, v):
        if not all(1 <= day <= 7 for day in v):
            raise ValueError("notification_days must contain values between 1 and 7")
        return sorted(list(set(v)))  # Remove duplicates and sort
    
    @validator("preferred_quote_length")
    def validate_quote_length(cls, v):
        allowed_lengths = ["short", "medium", "long", "any"]
        if v not in allowed_lengths:
            raise ValueError(f"preferred_quote_length must be one of: {', '.join(allowed_lengths)}")
        return v
    
    @validator("font_size")
    def validate_font_size(cls, v):
        allowed_sizes = ["small", "medium", "large"]
        if v not in allowed_sizes:
            raise ValueError(f"font_size must be one of: {', '.join(allowed_sizes)}")
        return v
    
    @validator("theme_preference")
    def validate_theme_preference(cls, v):
        allowed_themes = ["light", "dark", "system"]
        if v not in allowed_themes:
            raise ValueError(f"theme_preference must be one of: {', '.join(allowed_themes)}")
        return v
    
    @validator("background_type")
    def validate_background_type(cls, v):
        allowed_types = ["image", "color", "gradient"]
        if v not in allowed_types:
            raise ValueError(f"background_type must be one of: {', '.join(allowed_types)}")
        return v


class UserPreferenceCreate(UserPreferenceBase):
    """Schema for creating user preferences."""
    category_id: Optional[int] = None


class UserPreferenceUpdate(BaseModel):
    """Schema for updating user preferences."""
    category_id: Optional[int] = None
    notification_enabled: Optional[bool] = None
    notification_frequency: Optional[str] = None
    notification_time: Optional[time] = None
    notification_days: Optional[List[int]] = None
    preferred_quote_length: Optional[str] = None
    preferred_authors: Optional[List[str]] = None
    excluded_authors: Optional[List[str]] = None
    preferred_tags: Optional[List[str]] = None
    font_size: Optional[str] = None
    theme_preference: Optional[str] = None
    background_type: Optional[str] = None
    background_value: Optional[str] = None
    gamification_enabled: Optional[bool] = None
    streak_notifications: Optional[bool] = None
    achievement_notifications: Optional[bool] = None
    profile_public: Optional[bool] = None
    share_statistics: Optional[bool] = None
    allow_friend_requests: Optional[bool] = None
    show_author: Optional[bool] = None
    show_source: Optional[bool] = None
    auto_mark_read: Optional[bool] = None
    
    @validator("notification_frequency")
    def validate_notification_frequency(cls, v):
        if v is not None:
            allowed_frequencies = ["daily", "weekly", "custom", "never"]
            if v not in allowed_frequencies:
                raise ValueError(f"notification_frequency must be one of: {', '.join(allowed_frequencies)}")
        return v
    
    @validator("notification_days")
    def validate_notification_days(cls, v):
        if v is not None:
            if not all(1 <= day <= 7 for day in v):
                raise ValueError("notification_days must contain values between 1 and 7")
            return sorted(list(set(v)))
        return v
    
    @validator("preferred_quote_length")
    def validate_quote_length(cls, v):
        if v is not None:
            allowed_lengths = ["short", "medium", "long", "any"]
            if v not in allowed_lengths:
                raise ValueError(f"preferred_quote_length must be one of: {', '.join(allowed_lengths)}")
        return v
    
    @validator("font_size")
    def validate_font_size(cls, v):
        if v is not None:
            allowed_sizes = ["small", "medium", "large"]
            if v not in allowed_sizes:
                raise ValueError(f"font_size must be one of: {', '.join(allowed_sizes)}")
        return v
    
    @validator("theme_preference")
    def validate_theme_preference(cls, v):
        if v is not None:
            allowed_themes = ["light", "dark", "system"]
            if v not in allowed_themes:
                raise ValueError(f"theme_preference must be one of: {', '.join(allowed_themes)}")
        return v
    
    @validator("background_type")
    def validate_background_type(cls, v):
        if v is not None:
            allowed_types = ["image", "color", "gradient"]
            if v not in allowed_types:
                raise ValueError(f"background_type must be one of: {', '.join(allowed_types)}")
        return v


class UserPreferenceResponse(BaseModel):
    """Schema for user preference response."""
    id: int
    user_id: int
    category_id: Optional[int]
    notification_enabled: bool
    notification_frequency: str
    notification_time: str  # Time as string in HH:MM format
    notification_days: List[int]
    preferred_quote_length: str
    preferred_authors: List[str]
    excluded_authors: List[str]
    preferred_tags: List[str]
    font_size: str
    theme_preference: str
    background_type: str
    background_value: Optional[str]
    gamification_enabled: bool
    streak_notifications: bool
    achievement_notifications: bool
    profile_public: bool
    share_statistics: bool
    allow_friend_requests: bool
    show_author: bool
    show_source: bool
    auto_mark_read: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class NotificationSettings(BaseModel):
    """Schema for notification settings only."""
    notification_enabled: bool
    notification_frequency: str
    notification_time: time
    notification_days: List[int]
    streak_notifications: bool
    achievement_notifications: bool
    
    @validator("notification_frequency")
    def validate_notification_frequency(cls, v):
        allowed_frequencies = ["daily", "weekly", "custom", "never"]
        if v not in allowed_frequencies:
            raise ValueError(f"notification_frequency must be one of: {', '.join(allowed_frequencies)}")
        return v
    
    @validator("notification_days")
    def validate_notification_days(cls, v):
        if not all(1 <= day <= 7 for day in v):
            raise ValueError("notification_days must contain values between 1 and 7")
        return sorted(list(set(v)))


class DisplaySettings(BaseModel):
    """Schema for display settings only."""
    font_size: str
    theme_preference: str
    background_type: str
    background_value: Optional[str]
    show_author: bool
    show_source: bool
    
    @validator("font_size")
    def validate_font_size(cls, v):
        allowed_sizes = ["small", "medium", "large"]
        if v not in allowed_sizes:
            raise ValueError(f"font_size must be one of: {', '.join(allowed_sizes)}")
        return v
    
    @validator("theme_preference")
    def validate_theme_preference(cls, v):
        allowed_themes = ["light", "dark", "system"]
        if v not in allowed_themes:
            raise ValueError(f"theme_preference must be one of: {', '.join(allowed_themes)}")
        return v
    
    @validator("background_type")
    def validate_background_type(cls, v):
        allowed_types = ["image", "color", "gradient"]
        if v not in allowed_types:
            raise ValueError(f"background_type must be one of: {', '.join(allowed_types)}")
        return v
