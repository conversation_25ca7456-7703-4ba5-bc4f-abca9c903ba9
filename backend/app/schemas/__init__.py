"""
Pydantic schemas for request/response validation.
"""

from .user import UserCreate, UserUpdate, UserResponse, UserLogin
from .quote import QuoteCreate, QuoteUpdate, QuoteResponse, QuoteSearch
from .category import CategoryCreate, CategoryUpdate, CategoryResponse
from .user_preference import UserPreferenceCreate, UserPreferenceUpdate, UserPreferenceResponse
from .analytics import AnalyticsCreate, AnalyticsResponse, UserActivityCreate, QuoteInteractionCreate

__all__ = [
    "UserCreate",
    "UserUpdate", 
    "UserResponse",
    "UserLogin",
    "QuoteCreate",
    "QuoteUpdate",
    "QuoteResponse",
    "QuoteSearch",
    "CategoryCreate",
    "CategoryUpdate",
    "CategoryResponse",
    "UserPreferenceCreate",
    "UserPreferenceUpdate",
    "UserPreferenceResponse",
    "AnalyticsCreate",
    "AnalyticsResponse",
    "UserActivityCreate",
    "QuoteInteractionCreate"
]
