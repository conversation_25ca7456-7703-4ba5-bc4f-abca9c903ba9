"""
Analytics schemas for request/response validation.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, validator


class AnalyticsBase(BaseModel):
    """Base analytics schema with common fields."""
    metric_name: str
    metric_value: float
    metric_type: str = "counter"
    category: Optional[str] = None
    subcategory: Optional[str] = None
    tags: Optional[Dict[str, Any]] = None
    
    @validator("metric_type")
    def validate_metric_type(cls, v):
        allowed_types = ["counter", "gauge", "histogram"]
        if v not in allowed_types:
            raise ValueError(f"metric_type must be one of: {', '.join(allowed_types)}")
        return v


class AnalyticsCreate(AnalyticsBase):
    """Schema for creating analytics records."""
    user_id: Optional[int] = None
    session_id: Optional[str] = None


class AnalyticsResponse(BaseModel):
    """Schema for analytics response."""
    id: int
    metric_name: str
    metric_value: float
    metric_type: str
    category: Optional[str]
    subcategory: Optional[str]
    tags: Optional[Dict[str, Any]]
    timestamp: datetime
    date: str
    hour: int
    user_id: Optional[int]
    session_id: Optional[str]
    
    class Config:
        from_attributes = True


class UserActivityCreate(BaseModel):
    """Schema for creating user activity records."""
    activity_type: str
    activity_data: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    platform: Optional[str] = None
    app_version: Optional[str] = None
    country: Optional[str] = None
    region: Optional[str] = None
    city: Optional[str] = None
    duration: Optional[int] = None
    session_id: Optional[str] = None
    is_new_session: bool = False
    
    @validator("activity_type")
    def validate_activity_type(cls, v):
        allowed_types = [
            "login", "logout", "quote_view", "quote_like", "quote_share",
            "quote_favorite", "quote_copy", "search", "category_browse",
            "profile_update", "settings_change", "app_open", "app_close"
        ]
        if v not in allowed_types:
            raise ValueError(f"activity_type must be one of: {', '.join(allowed_types)}")
        return v
    
    @validator("platform")
    def validate_platform(cls, v):
        if v is not None:
            allowed_platforms = ["web", "ios", "android", "desktop"]
            if v not in allowed_platforms:
                raise ValueError(f"platform must be one of: {', '.join(allowed_platforms)}")
        return v


class UserActivityResponse(BaseModel):
    """Schema for user activity response."""
    id: int
    user_id: int
    activity_type: str
    activity_data: Optional[Dict[str, Any]]
    ip_address: Optional[str]
    user_agent: Optional[str]
    platform: Optional[str]
    app_version: Optional[str]
    country: Optional[str]
    region: Optional[str]
    city: Optional[str]
    timestamp: datetime
    duration: Optional[int]
    session_id: Optional[str]
    is_new_session: bool
    
    class Config:
        from_attributes = True


class QuoteInteractionCreate(BaseModel):
    """Schema for creating quote interaction records."""
    quote_id: int
    interaction_type: str
    interaction_data: Optional[Dict[str, Any]] = None
    value: float = 1.0
    platform: Optional[str] = None
    source: Optional[str] = None
    time_spent: Optional[int] = None
    scroll_depth: Optional[float] = None
    session_id: Optional[str] = None
    
    @validator("interaction_type")
    def validate_interaction_type(cls, v):
        allowed_types = ["view", "like", "unlike", "share", "favorite", "unfavorite", "copy"]
        if v not in allowed_types:
            raise ValueError(f"interaction_type must be one of: {', '.join(allowed_types)}")
        return v
    
    @validator("platform")
    def validate_platform(cls, v):
        if v is not None:
            allowed_platforms = ["web", "ios", "android", "desktop"]
            if v not in allowed_platforms:
                raise ValueError(f"platform must be one of: {', '.join(allowed_platforms)}")
        return v
    
    @validator("scroll_depth")
    def validate_scroll_depth(cls, v):
        if v is not None and (v < 0 or v > 100):
            raise ValueError("scroll_depth must be between 0 and 100")
        return v


class QuoteInteractionResponse(BaseModel):
    """Schema for quote interaction response."""
    id: int
    user_id: int
    quote_id: int
    interaction_type: str
    interaction_data: Optional[Dict[str, Any]]
    value: float
    platform: Optional[str]
    source: Optional[str]
    timestamp: datetime
    session_id: Optional[str]
    time_spent: Optional[int]
    scroll_depth: Optional[float]
    
    class Config:
        from_attributes = True


class AnalyticsQuery(BaseModel):
    """Schema for analytics query parameters."""
    metric_name: Optional[str] = None
    category: Optional[str] = None
    subcategory: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    user_id: Optional[int] = None
    group_by: Optional[str] = None
    aggregation: str = "sum"
    
    @validator("aggregation")
    def validate_aggregation(cls, v):
        allowed_aggregations = ["sum", "avg", "count", "min", "max"]
        if v not in allowed_aggregations:
            raise ValueError(f"aggregation must be one of: {', '.join(allowed_aggregations)}")
        return v
    
    @validator("group_by")
    def validate_group_by(cls, v):
        if v is not None:
            allowed_groups = ["date", "hour", "category", "user_id", "platform"]
            if v not in allowed_groups:
                raise ValueError(f"group_by must be one of: {', '.join(allowed_groups)}")
        return v


class AnalyticsResult(BaseModel):
    """Schema for analytics query results."""
    metric_name: str
    results: List[Dict[str, Any]]
    total_records: int
    aggregation: str
    group_by: Optional[str]
    
    class Config:
        from_attributes = True


class DashboardStats(BaseModel):
    """Schema for dashboard statistics."""
    total_users: int
    active_users_today: int
    active_users_week: int
    active_users_month: int
    total_quotes: int
    total_quote_views: int
    total_quote_likes: int
    total_quote_shares: int
    total_quote_favorites: int
    avg_session_duration: float
    top_categories: List[Dict[str, Any]]
    recent_activities: List[UserActivityResponse]
    
    class Config:
        from_attributes = True


class UserStats(BaseModel):
    """Schema for individual user statistics."""
    user_id: int
    total_sessions: int
    total_time_spent: int
    quotes_viewed: int
    quotes_liked: int
    quotes_shared: int
    quotes_favorited: int
    current_streak: int
    longest_streak: int
    favorite_categories: List[str]
    most_active_day: str
    most_active_hour: int
    
    class Config:
        from_attributes = True
