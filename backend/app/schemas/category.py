"""
Category schemas for request/response validation.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, validator


class CategoryBase(BaseModel):
    """Base category schema with common fields."""
    name: str
    description: Optional[str] = None
    color: Optional[str] = None
    icon: Optional[str] = None
    image_url: Optional[str] = None
    sort_order: int = 0
    
    @validator("name")
    def validate_name(cls, v):
        if len(v.strip()) < 2:
            raise ValueError("Category name must be at least 2 characters long")
        if len(v) > 100:
            raise ValueError("Category name must be less than 100 characters")
        return v.strip()
    
    @validator("color")
    def validate_color(cls, v):
        if v is not None:
            # Simple hex color validation
            if not v.startswith("#") or len(v) != 7:
                raise ValueError("Color must be a valid hex color code (e.g., #FF0000)")
            try:
                int(v[1:], 16)
            except ValueError:
                raise ValueError("Color must be a valid hex color code")
        return v
    
    @validator("sort_order")
    def validate_sort_order(cls, v):
        if v < 0:
            raise ValueError("Sort order must be non-negative")
        return v


class CategoryCreate(CategoryBase):
    """Schema for creating a new category."""
    pass


class CategoryUpdate(BaseModel):
    """Schema for updating category information."""
    name: Optional[str] = None
    description: Optional[str] = None
    color: Optional[str] = None
    icon: Optional[str] = None
    image_url: Optional[str] = None
    is_active: Optional[bool] = None
    sort_order: Optional[int] = None
    
    @validator("name")
    def validate_name(cls, v):
        if v is not None:
            if len(v.strip()) < 2:
                raise ValueError("Category name must be at least 2 characters long")
            if len(v) > 100:
                raise ValueError("Category name must be less than 100 characters")
            return v.strip()
        return v
    
    @validator("color")
    def validate_color(cls, v):
        if v is not None:
            if not v.startswith("#") or len(v) != 7:
                raise ValueError("Color must be a valid hex color code (e.g., #FF0000)")
            try:
                int(v[1:], 16)
            except ValueError:
                raise ValueError("Color must be a valid hex color code")
        return v
    
    @validator("sort_order")
    def validate_sort_order(cls, v):
        if v is not None and v < 0:
            raise ValueError("Sort order must be non-negative")
        return v


class CategoryResponse(BaseModel):
    """Schema for category response."""
    id: int
    name: str
    slug: str
    description: Optional[str]
    color: Optional[str]
    icon: Optional[str]
    image_url: Optional[str]
    is_active: bool
    sort_order: int
    quote_count: int
    popularity_score: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class CategoryWithQuotes(CategoryResponse):
    """Schema for category response with quotes."""
    quotes: list["QuoteResponse"] = []
    
    class Config:
        from_attributes = True


class CategoryStats(BaseModel):
    """Schema for category statistics."""
    total_quotes: int
    total_views: int
    total_likes: int
    total_shares: int
    total_favorites: int
    avg_quality_score: float
    most_popular_quote: Optional["QuoteResponse"]
    recent_quotes: list["QuoteResponse"]
    
    class Config:
        from_attributes = True


# Import QuoteResponse to avoid circular import issues
from .quote import QuoteResponse
CategoryWithQuotes.model_rebuild()
CategoryStats.model_rebuild()
