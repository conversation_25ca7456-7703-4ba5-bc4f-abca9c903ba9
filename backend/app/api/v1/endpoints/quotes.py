"""
Quote endpoints for the Daily Motivator API.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas.quote import (
    QuoteCreate, QuoteUpdate, QuoteResponse, QuoteWithStats,
    QuoteSearch, QuoteList, RandomQuoteRequest, QuoteInteraction
)
from app.services.quote_service import QuoteService
from app.services.analytics_service import AnalyticsService
from app.api.deps import get_current_user, get_current_active_user
from app.models.user import User

router = APIRouter()


@router.get("/", response_model=QuoteList)
async def get_quotes(
    skip: int = Query(0, ge=0, description="Number of quotes to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of quotes to return"),
    category_id: Optional[int] = Query(None, description="Filter by category ID"),
    author: Optional[str] = Query(None, description="Filter by author"),
    search: Optional[str] = Query(None, description="Search in quote text"),
    featured_only: bool = Query(False, description="Return only featured quotes"),
    db: Session = Depends(get_db)
):
    """Get a list of quotes with optional filtering."""
    quote_service = QuoteService(db)
    
    search_params = QuoteSearch(
        query=search,
        author=author,
        category_id=category_id,
        is_featured=featured_only if featured_only else None,
        page=(skip // limit) + 1,
        page_size=limit
    )
    
    return quote_service.search_quotes(search_params)


@router.get("/random", response_model=QuoteResponse)
async def get_random_quote(
    category_id: Optional[int] = Query(None, description="Category ID for random quote"),
    exclude_recent: bool = Query(True, description="Exclude recently shown quotes"),
    current_user: Optional[User] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a random quote, optionally filtered by category and user preferences."""
    quote_service = QuoteService(db)
    analytics_service = AnalyticsService(db)
    
    request = RandomQuoteRequest(
        category_id=category_id,
        exclude_recent=exclude_recent,
        user_preferences=current_user is not None
    )
    
    quote = quote_service.get_random_quote(request, current_user)
    
    if not quote:
        raise HTTPException(status_code=404, detail="No quotes found")
    
    # Track quote view
    if current_user:
        analytics_service.track_quote_interaction(
            user_id=current_user.id,
            quote_id=quote.id,
            interaction_type="view"
        )
    
    return quote


@router.get("/{quote_id}", response_model=QuoteWithStats)
async def get_quote(
    quote_id: int,
    current_user: Optional[User] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific quote by ID."""
    quote_service = QuoteService(db)
    analytics_service = AnalyticsService(db)
    
    quote = quote_service.get_quote_by_id(quote_id, include_stats=True)
    
    if not quote:
        raise HTTPException(status_code=404, detail="Quote not found")
    
    # Track quote view
    if current_user:
        analytics_service.track_quote_interaction(
            user_id=current_user.id,
            quote_id=quote_id,
            interaction_type="view"
        )
    
    return quote


@router.post("/", response_model=QuoteResponse)
async def create_quote(
    quote_data: QuoteCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new quote (admin only)."""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    quote_service = QuoteService(db)
    return quote_service.create_quote(quote_data)


@router.put("/{quote_id}", response_model=QuoteResponse)
async def update_quote(
    quote_id: int,
    quote_data: QuoteUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a quote (admin only)."""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    quote_service = QuoteService(db)
    quote = quote_service.update_quote(quote_id, quote_data)
    
    if not quote:
        raise HTTPException(status_code=404, detail="Quote not found")
    
    return quote


@router.delete("/{quote_id}")
async def delete_quote(
    quote_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a quote (admin only)."""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    quote_service = QuoteService(db)
    success = quote_service.delete_quote(quote_id)
    
    if not success:
        raise HTTPException(status_code=404, detail="Quote not found")
    
    return {"message": "Quote deleted successfully"}


@router.post("/{quote_id}/interact")
async def interact_with_quote(
    quote_id: int,
    interaction: QuoteInteraction,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Record an interaction with a quote (like, share, favorite, etc.)."""
    quote_service = QuoteService(db)
    analytics_service = AnalyticsService(db)
    
    # Verify quote exists
    quote = quote_service.get_quote_by_id(quote_id)
    if not quote:
        raise HTTPException(status_code=404, detail="Quote not found")
    
    # Process the interaction
    success = quote_service.process_interaction(
        quote_id=quote_id,
        user_id=current_user.id,
        interaction_type=interaction.interaction_type
    )
    
    if not success:
        raise HTTPException(status_code=400, detail="Failed to process interaction")
    
    # Track the interaction
    analytics_service.track_quote_interaction(
        user_id=current_user.id,
        quote_id=quote_id,
        interaction_type=interaction.interaction_type,
        platform=interaction.platform,
        source=interaction.source,
        time_spent=interaction.time_spent,
        scroll_depth=interaction.scroll_depth
    )
    
    return {"message": f"Interaction '{interaction.interaction_type}' recorded successfully"}


@router.get("/authors/", response_model=List[str])
async def get_authors(
    search: Optional[str] = Query(None, description="Search authors by name"),
    limit: int = Query(50, ge=1, le=100, description="Number of authors to return"),
    db: Session = Depends(get_db)
):
    """Get a list of quote authors."""
    quote_service = QuoteService(db)
    return quote_service.get_authors(search=search, limit=limit)


@router.get("/tags/", response_model=List[str])
async def get_tags(
    search: Optional[str] = Query(None, description="Search tags"),
    limit: int = Query(50, ge=1, le=100, description="Number of tags to return"),
    db: Session = Depends(get_db)
):
    """Get a list of quote tags."""
    quote_service = QuoteService(db)
    return quote_service.get_tags(search=search, limit=limit)


@router.get("/featured/", response_model=List[QuoteResponse])
async def get_featured_quotes(
    limit: int = Query(10, ge=1, le=50, description="Number of featured quotes to return"),
    db: Session = Depends(get_db)
):
    """Get featured quotes."""
    quote_service = QuoteService(db)
    return quote_service.get_featured_quotes(limit=limit)


@router.get("/popular/", response_model=List[QuoteWithStats])
async def get_popular_quotes(
    period: str = Query("week", description="Time period: day, week, month, all"),
    limit: int = Query(10, ge=1, le=50, description="Number of popular quotes to return"),
    db: Session = Depends(get_db)
):
    """Get popular quotes based on engagement metrics."""
    quote_service = QuoteService(db)
    return quote_service.get_popular_quotes(period=period, limit=limit)
