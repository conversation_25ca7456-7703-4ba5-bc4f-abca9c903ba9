"""
Analytics endpoints for the Daily Motivator API.
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas.analytics import DashboardStats, UserStats
from app.services.analytics_service import AnalyticsService
from app.api.deps import get_current_active_user, get_current_superuser
from app.models.user import User

router = APIRouter()


@router.get("/dashboard", response_model=DashboardStats)
async def get_dashboard_stats(
    current_user: User = Depends(get_current_superuser),
    db: Session = Depends(get_db)
):
    """Get dashboard statistics (admin only)."""
    analytics_service = AnalyticsService(db)
    return analytics_service.get_dashboard_stats()


@router.get("/user/{user_id}", response_model=UserStats)
async def get_user_analytics(
    user_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get user analytics."""
    # Users can only see their own analytics unless they're admin
    if current_user.id != user_id and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    analytics_service = AnalyticsService(db)
    stats = analytics_service.get_user_stats(user_id)
    
    if not stats:
        raise HTTPException(status_code=404, detail="User not found")
    
    return stats


@router.post("/track")
async def track_metric(
    metric_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Track a custom metric."""
    analytics_service = AnalyticsService(db)
    
    # Basic metric tracking
    analytics_service.track_metric(
        metric_name=metric_data.get("metric_name", "custom_metric"),
        metric_value=metric_data.get("metric_value", 1.0),
        category=metric_data.get("category"),
        user_id=current_user.id
    )
    
    return {"message": "Metric tracked successfully"}
