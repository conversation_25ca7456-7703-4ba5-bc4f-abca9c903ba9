"""
User preferences endpoints for the Daily Motivator API.
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.api.deps import get_current_active_user
from app.models.user import User

router = APIRouter()


@router.get("/")
async def get_user_preferences(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get current user's preferences."""
    # Placeholder implementation
    return {
        "user_id": current_user.id,
        "notification_enabled": current_user.email_notifications,
        "theme_preference": current_user.theme_preference,
        "language": current_user.language,
        "timezone": current_user.timezone
    }


@router.put("/")
async def update_user_preferences(
    preferences_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update current user's preferences."""
    # Placeholder implementation
    return {"message": "Preferences updated successfully"}
