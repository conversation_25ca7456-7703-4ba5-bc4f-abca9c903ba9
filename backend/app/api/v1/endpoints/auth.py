"""
Authentication endpoints for the Daily Motivator API.
"""

from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.database import get_db
from app.core.security import (
    create_access_token, verify_password, get_password_hash,
    create_password_reset_token, verify_password_reset_token,
    create_email_verification_token, verify_email_verification_token
)
from app.schemas.user import (
    UserCreate, UserResponse, UserLogin, 
    PasswordReset, PasswordResetConfirm, EmailVerification
)
from app.services.user_service import UserService
from app.services.analytics_service import AnalyticsService
from app.api.deps import auth_rate_limiter, get_current_active_user
from app.models.user import User

router = APIRouter()


@router.post("/register", response_model=UserResponse)
async def register(
    user_data: User<PERSON>reate,
    request: Request,
    db: Session = Depends(get_db),
    _: bool = Depends(auth_rate_limiter)
):
    """Register a new user."""
    user_service = UserService(db)
    analytics_service = AnalyticsService(db)
    
    # Check if user already exists
    existing_user = user_service.get_user_by_email(user_data.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    existing_username = user_service.get_user_by_username(user_data.username)
    if existing_username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already taken"
        )
    
    # Create user
    user = user_service.create_user(user_data)
    
    # Track registration
    analytics_service.track_user_activity(
        user_id=user.id,
        activity_type="register",
        ip_address=request.client.host,
        user_agent=request.headers.get("user-agent"),
        is_new_session=True
    )
    
    return user


@router.post("/login")
async def login(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db),
    _: bool = Depends(auth_rate_limiter)
):
    """Login and get access token."""
    user_service = UserService(db)
    analytics_service = AnalyticsService(db)
    
    # Authenticate user (username can be email or username)
    user = user_service.authenticate_user(form_data.username, form_data.password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=user.id, expires_delta=access_token_expires
    )
    
    # Update user login info
    user_service.update_last_login(user.id)
    
    # Track login
    analytics_service.track_user_activity(
        user_id=user.id,
        activity_type="login",
        ip_address=request.client.host,
        user_agent=request.headers.get("user-agent"),
        is_new_session=True
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user": user.to_dict()
    }


@router.post("/login-json")
async def login_json(
    user_credentials: UserLogin,
    request: Request,
    db: Session = Depends(get_db),
    _: bool = Depends(auth_rate_limiter)
):
    """Login with JSON payload and get access token."""
    user_service = UserService(db)
    analytics_service = AnalyticsService(db)
    
    # Authenticate user
    user = user_service.authenticate_user(
        user_credentials.username, 
        user_credentials.password
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=user.id, expires_delta=access_token_expires
    )
    
    # Update user login info
    user_service.update_last_login(user.id)
    
    # Track login
    analytics_service.track_user_activity(
        user_id=user.id,
        activity_type="login",
        ip_address=request.client.host,
        user_agent=request.headers.get("user-agent"),
        is_new_session=True
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user": user.to_dict()
    }


@router.post("/logout")
async def logout(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Logout user (mainly for tracking purposes)."""
    analytics_service = AnalyticsService(db)
    
    # Track logout
    analytics_service.track_user_activity(
        user_id=current_user.id,
        activity_type="logout",
        ip_address=request.client.host,
        user_agent=request.headers.get("user-agent")
    )
    
    return {"message": "Successfully logged out"}


@router.post("/refresh")
async def refresh_token(
    current_user: User = Depends(get_current_active_user)
):
    """Refresh access token."""
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=current_user.id, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }


@router.post("/password-reset")
async def request_password_reset(
    password_reset: PasswordReset,
    db: Session = Depends(get_db),
    _: bool = Depends(auth_rate_limiter)
):
    """Request password reset."""
    user_service = UserService(db)
    
    user = user_service.get_user_by_email(password_reset.email)
    if not user:
        # Don't reveal if email exists or not
        return {"message": "If the email exists, a password reset link has been sent"}
    
    # Generate password reset token
    reset_token = create_password_reset_token(user.email)
    
    # In a real application, you would send this token via email
    # For now, we'll just store it in the user record
    user_service.set_password_reset_token(user.id, reset_token)
    
    return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/password-reset-confirm")
async def confirm_password_reset(
    password_reset_confirm: PasswordResetConfirm,
    db: Session = Depends(get_db),
    _: bool = Depends(auth_rate_limiter)
):
    """Confirm password reset with token."""
    user_service = UserService(db)
    
    # Verify token
    email = verify_password_reset_token(password_reset_confirm.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired token"
        )
    
    user = user_service.get_user_by_email(email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid token"
        )
    
    # Update password
    hashed_password = get_password_hash(password_reset_confirm.new_password)
    user_service.update_password(user.id, hashed_password)
    
    # Clear reset token
    user_service.clear_password_reset_token(user.id)
    
    return {"message": "Password updated successfully"}


@router.post("/verify-email")
async def verify_email(
    email_verification: EmailVerification,
    db: Session = Depends(get_db)
):
    """Verify email address."""
    user_service = UserService(db)
    
    # Verify token
    email = verify_email_verification_token(email_verification.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired token"
        )
    
    user = user_service.get_user_by_email(email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid token"
        )
    
    if user.is_verified:
        return {"message": "Email already verified"}
    
    # Verify user
    user_service.verify_user(user.id)
    
    return {"message": "Email verified successfully"}


@router.post("/resend-verification")
async def resend_verification_email(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    _: bool = Depends(auth_rate_limiter)
):
    """Resend email verification."""
    if current_user.is_verified:
        return {"message": "Email already verified"}
    
    # Generate verification token
    verification_token = create_email_verification_token(current_user.email)
    
    # In a real application, you would send this token via email
    # For now, we'll just return success
    
    return {"message": "Verification email sent"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user information."""
    return current_user
