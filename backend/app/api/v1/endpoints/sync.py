"""
Sync endpoints for offline/online synchronization.
Handles data sync between mobile app and server for offline functionality.
"""

from datetime import datetime, timedelta
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.api.deps import get_current_user_optional, get_db
from app.models.quote import Quote
from app.models.category import Category
from app.models.user import User
from app.schemas.sync import (
    SyncRequest,
    SyncResponse,
    QuoteSync,
    CategorySync,
    OfflineQuoteRequest,
    WidgetQuoteResponse
)

router = APIRouter()


@router.post("/quotes", response_model=SyncResponse)
async def sync_quotes(
    sync_request: SyncRequest,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """
    Sync quotes for offline usage.
    Returns quotes updated since last sync timestamp.
    """
    try:
        # Get last sync timestamp
        last_sync = sync_request.last_sync_timestamp
        if last_sync:
            last_sync_dt = datetime.fromisoformat(last_sync.replace('Z', '+00:00'))
        else:
            # If no last sync, get quotes from last 30 days
            last_sync_dt = datetime.utcnow() - timedelta(days=30)
        
        # Query for updated quotes
        query = db.query(Quote).filter(
            and_(
                Quote.is_approved == True,
                Quote.updated_at > last_sync_dt
            )
        )
        
        # Filter by categories if specified
        if sync_request.category_ids:
            query = query.filter(Quote.category_id.in_(sync_request.category_ids))
        
        # Limit results
        limit = min(sync_request.limit or 100, 500)  # Max 500 quotes per sync
        quotes = query.order_by(Quote.updated_at.desc()).limit(limit).all()
        
        # Convert to sync format
        quote_syncs = []
        for quote in quotes:
            quote_sync = QuoteSync(
                id=quote.id,
                text=quote.text,
                author=quote.author,
                category_id=quote.category_id,
                category_name=quote.category.name if quote.category else "",
                tags=quote.get_tags_list(),
                is_featured=quote.is_featured,
                quality_score=quote.quality_score,
                created_at=quote.created_at.isoformat(),
                updated_at=quote.updated_at.isoformat()
            )
            quote_syncs.append(quote_sync)
        
        return SyncResponse(
            quotes=quote_syncs,
            total_count=len(quote_syncs),
            sync_timestamp=datetime.utcnow().isoformat(),
            has_more=len(quote_syncs) == limit
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Sync failed: {str(e)}")


@router.post("/categories", response_model=List[CategorySync])
async def sync_categories(
    last_sync: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Sync categories for offline usage.
    """
    try:
        query = db.query(Category).filter(Category.is_active == True)
        
        if last_sync:
            last_sync_dt = datetime.fromisoformat(last_sync.replace('Z', '+00:00'))
            query = query.filter(Category.updated_at > last_sync_dt)
        
        categories = query.order_by(Category.sort_order).all()
        
        category_syncs = []
        for category in categories:
            category_sync = CategorySync(
                id=category.id,
                name=category.name,
                slug=category.slug,
                description=category.description,
                color=category.color,
                icon=category.icon,
                quote_count=category.quote_count,
                sort_order=category.sort_order,
                updated_at=category.updated_at.isoformat()
            )
            category_syncs.append(category_sync)
        
        return category_syncs
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Category sync failed: {str(e)}")


@router.get("/offline-quotes", response_model=List[QuoteSync])
async def get_offline_quotes(
    categories: Optional[str] = Query(None, description="Comma-separated category IDs"),
    count: int = Query(50, ge=1, le=200, description="Number of quotes to return"),
    featured_only: bool = Query(False, description="Return only featured quotes"),
    db: Session = Depends(get_db)
):
    """
    Get quotes for offline storage.
    Optimized for mobile app offline functionality.
    """
    try:
        query = db.query(Quote).filter(Quote.is_approved == True)
        
        if featured_only:
            query = query.filter(Quote.is_featured == True)
        
        if categories:
            category_ids = [int(id.strip()) for id in categories.split(",") if id.strip().isdigit()]
            if category_ids:
                query = query.filter(Quote.category_id.in_(category_ids))
        
        # Order by quality score and recency
        quotes = query.order_by(
            Quote.quality_score.desc(),
            Quote.created_at.desc()
        ).limit(count).all()
        
        quote_syncs = []
        for quote in quotes:
            quote_sync = QuoteSync(
                id=quote.id,
                text=quote.text,
                author=quote.author,
                category_id=quote.category_id,
                category_name=quote.category.name if quote.category else "",
                tags=quote.get_tags_list(),
                is_featured=quote.is_featured,
                quality_score=quote.quality_score,
                created_at=quote.created_at.isoformat(),
                updated_at=quote.updated_at.isoformat()
            )
            quote_syncs.append(quote_sync)
        
        return quote_syncs
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get offline quotes: {str(e)}")


@router.get("/widget-quote", response_model=WidgetQuoteResponse)
async def get_widget_quote(
    category: Optional[str] = Query(None, description="Category slug"),
    db: Session = Depends(get_db)
):
    """
    Get a single quote for Android home screen widget.
    Optimized for widget display.
    """
    try:
        query = db.query(Quote).filter(Quote.is_approved == True)
        
        if category:
            # Find category by slug
            cat = db.query(Category).filter(Category.slug == category).first()
            if cat:
                query = query.filter(Quote.category_id == cat.id)
        
        # Get a random high-quality quote
        quote = query.filter(Quote.quality_score > 0).order_by(
            Quote.quality_score.desc()
        ).first()
        
        if not quote:
            # Fallback to any approved quote
            quote = query.first()
        
        if not quote:
            raise HTTPException(status_code=404, detail="No quotes available")
        
        # Update view count
        quote.increment_view_count()
        db.commit()
        
        return WidgetQuoteResponse(
            id=quote.id,
            text=quote.text,
            author=quote.author,
            category=quote.category.name if quote.category else "General",
            category_color=quote.category.color if quote.category else "#4ECDC4"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get widget quote: {str(e)}")


@router.post("/analytics")
async def sync_analytics(
    analytics_data: dict,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """
    Sync analytics data from mobile app.
    Accepts offline analytics data and processes it.
    """
    try:
        # Process analytics data
        # This could include quote views, interactions, etc.
        
        # For now, just acknowledge receipt
        return {"status": "success", "message": "Analytics data received"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analytics sync failed: {str(e)}")


@router.get("/health")
async def sync_health_check():
    """Health check for sync service."""
    return {
        "status": "healthy",
        "service": "sync",
        "timestamp": datetime.utcnow().isoformat()
    }
