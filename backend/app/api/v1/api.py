"""
API v1 router configuration.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, quotes, categories, preferences, analytics, sync

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(quotes.router, prefix="/quotes", tags=["quotes"])
api_router.include_router(categories.router, prefix="/categories", tags=["categories"])
api_router.include_router(preferences.router, prefix="/preferences", tags=["preferences"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
api_router.include_router(sync.router, prefix="/sync", tags=["sync"])
