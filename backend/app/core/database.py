"""
Database configuration and connection management.
Handles SQLAlchemy setup, session management, and database initialization.
"""

import logging
from typing import Generator, Optional
from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from .config import get_database_url, settings

logger = logging.getLogger(__name__)

# SQLAlchemy Base
Base = declarative_base()

# Database engine
engine = None
SessionLocal = None


def create_database_engine(database_url: Optional[str] = None, echo: Optional[bool] = None):
    """Create and configure database engine."""
    global engine
    
    db_url = database_url or get_database_url()
    db_echo = echo if echo is not None else settings.DATABASE_ECHO
    
    logger.info(f"Creating database engine for: {db_url}")
    
    # SQLite specific configuration
    if db_url.startswith("sqlite"):
        engine = create_engine(
            db_url,
            echo=db_echo,
            connect_args={
                "check_same_thread": False,  # Allow SQLite to be used with multiple threads
                "timeout": 20,  # Connection timeout
            },
            poolclass=StaticPool,  # Use static pool for SQLite
        )
        
        # Enable foreign key constraints for SQLite
        @event.listens_for(engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            cursor = dbapi_connection.cursor()
            cursor.execute("PRAGMA foreign_keys=ON")
            cursor.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging for better concurrency
            cursor.execute("PRAGMA synchronous=NORMAL")  # Balance between safety and speed
            cursor.execute("PRAGMA cache_size=1000")  # Increase cache size
            cursor.execute("PRAGMA temp_store=MEMORY")  # Store temp tables in memory
            cursor.close()
            
    else:
        # For other databases (PostgreSQL, MySQL, etc.)
        engine = create_engine(
            db_url,
            echo=db_echo,
            pool_pre_ping=True,  # Verify connections before use
            pool_recycle=300,  # Recycle connections every 5 minutes
        )
    
    return engine


def create_session_factory():
    """Create session factory."""
    global SessionLocal
    
    if engine is None:
        create_database_engine()
    
    SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine
    )
    
    return SessionLocal


def get_db() -> Generator[Session, None, None]:
    """
    Dependency to get database session.
    Use this in FastAPI endpoints to get a database session.
    """
    if SessionLocal is None:
        create_session_factory()
    
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def init_db() -> None:
    """Initialize database tables."""
    if engine is None:
        create_database_engine()
    
    logger.info("Creating database tables...")
    
    # Import all models to ensure they are registered with SQLAlchemy
    from app.models import user, quote, category, user_preference, analytics  # noqa
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    logger.info("Database tables created successfully")


def drop_db() -> None:
    """Drop all database tables. Use with caution!"""
    if engine is None:
        create_database_engine()
    
    logger.warning("Dropping all database tables...")
    Base.metadata.drop_all(bind=engine)
    logger.info("All database tables dropped")


def reset_db() -> None:
    """Reset database by dropping and recreating all tables."""
    logger.info("Resetting database...")
    drop_db()
    init_db()
    logger.info("Database reset completed")


class DatabaseManager:
    """Database manager for handling connections and operations."""
    
    def __init__(self, database_url: Optional[str] = None):
        self.database_url = database_url or get_database_url()
        self.engine = None
        self.session_factory = None
    
    def initialize(self):
        """Initialize database connection."""
        self.engine = create_database_engine(self.database_url)
        self.session_factory = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        logger.info("Database manager initialized")
    
    def create_tables(self):
        """Create all database tables."""
        if self.engine is None:
            self.initialize()
        
        # Import models
        from app.models import user, quote, category, user_preference, analytics  # noqa
        
        Base.metadata.create_all(bind=self.engine)
        logger.info("Database tables created")
    
    def get_session(self) -> Session:
        """Get a new database session."""
        if self.session_factory is None:
            self.initialize()
        
        return self.session_factory()
    
    def close(self):
        """Close database connections."""
        if self.engine:
            self.engine.dispose()
            logger.info("Database connections closed")


# Global database manager instance
db_manager = DatabaseManager()


def get_db_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    return db_manager


# Health check function
def check_database_health() -> bool:
    """Check if database is accessible and healthy."""
    try:
        if engine is None:
            create_database_engine()

        with engine.connect() as connection:
            # Simple query to test connection
            from sqlalchemy import text
            result = connection.execute(text("SELECT 1"))
            return result.fetchone()[0] == 1

    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False


# Context manager for database transactions
class DatabaseTransaction:
    """Context manager for database transactions."""
    
    def __init__(self, session: Optional[Session] = None):
        self.session = session
        self.should_close = session is None
    
    def __enter__(self) -> Session:
        if self.session is None:
            if SessionLocal is None:
                create_session_factory()
            self.session = SessionLocal()
        
        return self.session
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.session.rollback()
            logger.error(f"Transaction rolled back due to: {exc_val}")
        else:
            self.session.commit()
        
        if self.should_close:
            self.session.close()


# Initialize database on module import
def setup_database():
    """Setup database on application startup."""
    try:
        create_database_engine()
        create_session_factory()
        logger.info("Database setup completed")
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        raise


# Call setup on import
if settings.ENVIRONMENT != "testing":
    setup_database()
