"""
Quote service for handling quote-related business logic.
"""

import random
from datetime import datetime, timedelta
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, func, desc

from app.models.quote import Quote
from app.models.category import Category
from app.models.user import User
from app.schemas.quote import QuoteCreate, QuoteUpdate, QuoteSearch, QuoteList, RandomQuoteRequest


class QuoteService:
    """Service class for quote operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_quote_by_id(self, quote_id: int, include_stats: bool = False) -> Optional[Quote]:
        """Get quote by ID."""
        quote = self.db.query(Quote).filter(Quote.id == quote_id).first()
        if quote and not include_stats:
            # Don't include stats in basic response
            pass
        return quote
    
    def create_quote(self, quote_data: QuoteCreate) -> Quote:
        """Create a new quote."""
        # Convert tags list to comma-separated string
        tags_str = None
        if quote_data.tags:
            tags_str = ", ".join(quote_data.tags)
        
        db_quote = Quote(
            text=quote_data.text,
            author=quote_data.author,
            source=quote_data.source,
            category_id=quote_data.category_id,
            tags=tags_str,
            language=quote_data.language,
            source_url=quote_data.source_url,
            attribution_required=quote_data.attribution_required,
            copyright_info=quote_data.copyright_info,
        )
        
        self.db.add(db_quote)
        self.db.commit()
        self.db.refresh(db_quote)
        
        # Update category quote count
        category = self.db.query(Category).filter(Category.id == quote_data.category_id).first()
        if category:
            category.increment_quote_count()
            self.db.commit()
        
        return db_quote
    
    def update_quote(self, quote_id: int, quote_data: QuoteUpdate) -> Optional[Quote]:
        """Update quote information."""
        quote = self.get_quote_by_id(quote_id)
        if not quote:
            return None
        
        update_data = quote_data.dict(exclude_unset=True)
        
        # Handle tags conversion
        if "tags" in update_data and update_data["tags"] is not None:
            update_data["tags"] = ", ".join(update_data["tags"])
        
        for field, value in update_data.items():
            setattr(quote, field, value)
        
        quote.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(quote)
        
        return quote
    
    def delete_quote(self, quote_id: int) -> bool:
        """Delete a quote."""
        quote = self.get_quote_by_id(quote_id)
        if not quote:
            return False
        
        # Update category quote count
        category = self.db.query(Category).filter(Category.id == quote.category_id).first()
        if category:
            category.decrement_quote_count()
        
        self.db.delete(quote)
        self.db.commit()
        
        return True
    
    def search_quotes(self, search_params: QuoteSearch) -> QuoteList:
        """Search quotes with filtering and pagination."""
        query = self.db.query(Quote).filter(Quote.is_approved == True)
        
        # Apply filters
        if search_params.query:
            search_term = f"%{search_params.query}%"
            query = query.filter(
                or_(
                    Quote.text.ilike(search_term),
                    Quote.author.ilike(search_term),
                    Quote.tags.ilike(search_term)
                )
            )
        
        if search_params.author:
            query = query.filter(Quote.author.ilike(f"%{search_params.author}%"))
        
        if search_params.category_id:
            query = query.filter(Quote.category_id == search_params.category_id)
        
        if search_params.language:
            query = query.filter(Quote.language == search_params.language)
        
        if search_params.min_length:
            query = query.filter(Quote.length >= search_params.min_length)
        
        if search_params.max_length:
            query = query.filter(Quote.length <= search_params.max_length)
        
        if search_params.is_featured is not None:
            query = query.filter(Quote.is_featured == search_params.is_featured)
        
        # Apply sorting
        if search_params.sort_by == "created_at":
            sort_field = Quote.created_at
        elif search_params.sort_by == "updated_at":
            sort_field = Quote.updated_at
        elif search_params.sort_by == "author":
            sort_field = Quote.author
        elif search_params.sort_by == "quality_score":
            sort_field = Quote.quality_score
        elif search_params.sort_by == "view_count":
            sort_field = Quote.view_count
        elif search_params.sort_by == "like_count":
            sort_field = Quote.like_count
        elif search_params.sort_by == "share_count":
            sort_field = Quote.share_count
        elif search_params.sort_by == "favorite_count":
            sort_field = Quote.favorite_count
        else:
            sort_field = Quote.created_at
        
        if search_params.sort_order == "desc":
            query = query.order_by(desc(sort_field))
        else:
            query = query.order_by(sort_field)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (search_params.page - 1) * search_params.page_size
        quotes = query.offset(offset).limit(search_params.page_size).all()
        
        # Calculate total pages
        total_pages = (total + search_params.page_size - 1) // search_params.page_size
        
        return QuoteList(
            quotes=quotes,
            total=total,
            page=search_params.page,
            page_size=search_params.page_size,
            total_pages=total_pages
        )
    
    def get_random_quote(self, request: RandomQuoteRequest, user: Optional[User] = None) -> Optional[Quote]:
        """Get a random quote with optional filtering."""
        query = self.db.query(Quote).filter(Quote.is_approved == True)
        
        # Filter by category if specified
        if request.category_id:
            query = query.filter(Quote.category_id == request.category_id)
        
        # Exclude recently shown quotes if requested
        if request.exclude_recent and user:
            recent_cutoff = datetime.utcnow() - timedelta(hours=24)
            query = query.filter(
                or_(
                    Quote.last_shown.is_(None),
                    Quote.last_shown < recent_cutoff
                )
            )
        
        # Get all matching quotes
        quotes = query.all()
        
        if not quotes:
            return None
        
        # Select random quote
        quote = random.choice(quotes)
        
        # Update last shown timestamp
        quote.increment_view_count()
        self.db.commit()
        
        return quote
    
    def process_interaction(self, quote_id: int, user_id: int, interaction_type: str) -> bool:
        """Process a quote interaction."""
        quote = self.get_quote_by_id(quote_id)
        if not quote:
            return False
        
        if interaction_type == "like":
            quote.increment_like_count()
        elif interaction_type == "unlike":
            quote.decrement_like_count()
        elif interaction_type == "share":
            quote.increment_share_count()
        elif interaction_type == "favorite":
            quote.increment_favorite_count()
        elif interaction_type == "unfavorite":
            quote.decrement_favorite_count()
        elif interaction_type == "view":
            quote.increment_view_count()
        
        self.db.commit()
        return True
    
    def get_authors(self, search: Optional[str] = None, limit: int = 50) -> List[str]:
        """Get list of quote authors."""
        query = self.db.query(Quote.author).distinct()
        
        if search:
            query = query.filter(Quote.author.ilike(f"%{search}%"))
        
        authors = query.limit(limit).all()
        return [author[0] for author in authors]
    
    def get_tags(self, search: Optional[str] = None, limit: int = 50) -> List[str]:
        """Get list of quote tags."""
        # This is a simplified implementation
        # In a real app, you might want a separate tags table
        query = self.db.query(Quote.tags).filter(Quote.tags.isnot(None))
        
        if search:
            query = query.filter(Quote.tags.ilike(f"%{search}%"))
        
        tag_strings = query.limit(limit * 2).all()  # Get more to account for filtering
        
        all_tags = set()
        for tag_string in tag_strings:
            if tag_string[0]:
                tags = [tag.strip() for tag in tag_string[0].split(",")]
                all_tags.update(tags)
        
        # Filter by search term if provided
        if search:
            all_tags = {tag for tag in all_tags if search.lower() in tag.lower()}
        
        return sorted(list(all_tags))[:limit]
    
    def get_featured_quotes(self, limit: int = 10) -> List[Quote]:
        """Get featured quotes."""
        return self.db.query(Quote).filter(
            and_(Quote.is_approved == True, Quote.is_featured == True)
        ).order_by(desc(Quote.quality_score)).limit(limit).all()
    
    def get_popular_quotes(self, period: str = "week", limit: int = 10) -> List[Quote]:
        """Get popular quotes based on engagement metrics."""
        query = self.db.query(Quote).filter(Quote.is_approved == True)
        
        # Filter by time period if needed
        if period != "all":
            if period == "day":
                cutoff = datetime.utcnow() - timedelta(days=1)
            elif period == "week":
                cutoff = datetime.utcnow() - timedelta(weeks=1)
            elif period == "month":
                cutoff = datetime.utcnow() - timedelta(days=30)
            else:
                cutoff = datetime.utcnow() - timedelta(weeks=1)  # Default to week
            
            # For simplicity, we'll use last_shown as a proxy for recent activity
            query = query.filter(Quote.last_shown >= cutoff)
        
        # Order by quality score (which includes engagement metrics)
        return query.order_by(desc(Quote.quality_score)).limit(limit).all()
