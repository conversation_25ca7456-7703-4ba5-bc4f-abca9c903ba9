"""
User service for handling user-related business logic.
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import or_

from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash, verify_password


class UserService:
    """Service class for user operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID."""
        return self.db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        return self.db.query(User).filter(User.email == email.lower()).first()
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        return self.db.query(User).filter(User.username == username.lower()).first()
    
    def get_user_by_email_or_username(self, identifier: str) -> Optional[User]:
        """Get user by email or username."""
        return self.db.query(User).filter(
            or_(
                User.email == identifier.lower(),
                User.username == identifier.lower()
            )
        ).first()
    
    def create_user(self, user_data: UserCreate) -> User:
        """Create a new user."""
        hashed_password = get_password_hash(user_data.password)
        
        db_user = User(
            email=user_data.email.lower(),
            username=user_data.username.lower(),
            hashed_password=hashed_password,
            full_name=user_data.full_name,
            bio=user_data.bio,
            timezone=user_data.timezone,
            language=user_data.language,
            theme_preference=user_data.theme_preference,
        )
        
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)
        
        return db_user
    
    def update_user(self, user_id: int, user_data: UserUpdate) -> Optional[User]:
        """Update user information."""
        user = self.get_user_by_id(user_id)
        if not user:
            return None
        
        update_data = user_data.dict(exclude_unset=True)
        
        # Handle email and username uniqueness
        if "email" in update_data:
            existing_user = self.get_user_by_email(update_data["email"])
            if existing_user and existing_user.id != user_id:
                raise ValueError("Email already registered")
            update_data["email"] = update_data["email"].lower()
        
        if "username" in update_data:
            existing_user = self.get_user_by_username(update_data["username"])
            if existing_user and existing_user.id != user_id:
                raise ValueError("Username already taken")
            update_data["username"] = update_data["username"].lower()
        
        for field, value in update_data.items():
            setattr(user, field, value)
        
        user.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def delete_user(self, user_id: int) -> bool:
        """Delete a user."""
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        self.db.delete(user)
        self.db.commit()
        
        return True
    
    def authenticate_user(self, identifier: str, password: str) -> Optional[User]:
        """Authenticate user with email/username and password."""
        user = self.get_user_by_email_or_username(identifier)
        if not user:
            return None
        
        if not verify_password(password, user.hashed_password):
            return None
        
        return user
    
    def update_last_login(self, user_id: int) -> None:
        """Update user's last login timestamp."""
        user = self.get_user_by_id(user_id)
        if user:
            user.update_last_login()
            self.db.commit()
    
    def update_activity(self, user_id: int) -> None:
        """Update user's activity and streak."""
        user = self.get_user_by_id(user_id)
        if user:
            user.update_activity()
            self.db.commit()
    
    def add_points(self, user_id: int, points: int) -> None:
        """Add points to user."""
        user = self.get_user_by_id(user_id)
        if user:
            user.add_points(points)
            self.db.commit()
    
    def update_password(self, user_id: int, new_password: str) -> bool:
        """Update user password."""
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.hashed_password = get_password_hash(new_password)
        user.updated_at = datetime.utcnow()
        self.db.commit()
        
        return True
    
    def verify_user(self, user_id: int) -> bool:
        """Mark user as verified."""
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.is_verified = True
        user.verification_token = None
        user.verification_token_expires = None
        user.updated_at = datetime.utcnow()
        self.db.commit()
        
        return True
    
    def set_password_reset_token(self, user_id: int, token: str) -> bool:
        """Set password reset token for user."""
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.password_reset_token = token
        user.password_reset_expires = datetime.utcnow()
        self.db.commit()
        
        return True
    
    def clear_password_reset_token(self, user_id: int) -> bool:
        """Clear password reset token for user."""
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.password_reset_token = None
        user.password_reset_expires = None
        self.db.commit()
        
        return True
    
    def activate_user(self, user_id: int) -> bool:
        """Activate user account."""
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.is_active = True
        user.updated_at = datetime.utcnow()
        self.db.commit()
        
        return True
    
    def deactivate_user(self, user_id: int) -> bool:
        """Deactivate user account."""
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.is_active = False
        user.updated_at = datetime.utcnow()
        self.db.commit()
        
        return True
    
    def get_users(
        self, 
        skip: int = 0, 
        limit: int = 100,
        search: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_verified: Optional[bool] = None
    ) -> List[User]:
        """Get list of users with optional filtering."""
        query = self.db.query(User)
        
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    User.username.ilike(search_term),
                    User.email.ilike(search_term),
                    User.full_name.ilike(search_term)
                )
            )
        
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        if is_verified is not None:
            query = query.filter(User.is_verified == is_verified)
        
        return query.offset(skip).limit(limit).all()
    
    def get_user_count(
        self,
        search: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_verified: Optional[bool] = None
    ) -> int:
        """Get total count of users with optional filtering."""
        query = self.db.query(User)
        
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    User.username.ilike(search_term),
                    User.email.ilike(search_term),
                    User.full_name.ilike(search_term)
                )
            )
        
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        if is_verified is not None:
            query = query.filter(User.is_verified == is_verified)
        
        return query.count()
    
    def get_top_users_by_points(self, limit: int = 10) -> List[User]:
        """Get top users by points (leaderboard)."""
        return self.db.query(User).filter(
            User.is_active == True
        ).order_by(User.points.desc()).limit(limit).all()
    
    def get_top_users_by_streak(self, limit: int = 10) -> List[User]:
        """Get top users by current streak."""
        return self.db.query(User).filter(
            User.is_active == True
        ).order_by(User.streak_count.desc()).limit(limit).all()
