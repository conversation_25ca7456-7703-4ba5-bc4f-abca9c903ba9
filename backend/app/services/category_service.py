"""
Category service for handling category-related business logic.
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import or_, desc

from app.models.category import Category
from app.schemas.category import CategoryCreate, CategoryUpdate
from slugify import slugify


class CategoryService:
    """Service class for category operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_category_by_id(self, category_id: int) -> Optional[Category]:
        """Get category by ID."""
        return self.db.query(Category).filter(Category.id == category_id).first()
    
    def get_category_by_slug(self, slug: str) -> Optional[Category]:
        """Get category by slug."""
        return self.db.query(Category).filter(Category.slug == slug).first()
    
    def create_category(self, category_data: CategoryCreate) -> Category:
        """Create a new category."""
        # Generate slug from name
        slug = slugify(category_data.name)
        
        # Ensure slug is unique
        existing_category = self.get_category_by_slug(slug)
        if existing_category:
            # Add number suffix to make it unique
            counter = 1
            while existing_category:
                new_slug = f"{slug}-{counter}"
                existing_category = self.get_category_by_slug(new_slug)
                counter += 1
            slug = new_slug
        
        db_category = Category(
            name=category_data.name,
            slug=slug,
            description=category_data.description,
            color=category_data.color,
            icon=category_data.icon,
            image_url=category_data.image_url,
            sort_order=category_data.sort_order,
        )
        
        self.db.add(db_category)
        self.db.commit()
        self.db.refresh(db_category)
        
        return db_category
    
    def update_category(self, category_id: int, category_data: CategoryUpdate) -> Optional[Category]:
        """Update category information."""
        category = self.get_category_by_id(category_id)
        if not category:
            return None
        
        update_data = category_data.dict(exclude_unset=True)
        
        # Handle name change - update slug if name changed
        if "name" in update_data:
            new_slug = slugify(update_data["name"])
            if new_slug != category.slug:
                # Check if new slug is unique
                existing_category = self.get_category_by_slug(new_slug)
                if existing_category and existing_category.id != category_id:
                    # Add number suffix to make it unique
                    counter = 1
                    while existing_category:
                        test_slug = f"{new_slug}-{counter}"
                        existing_category = self.get_category_by_slug(test_slug)
                        if not existing_category or existing_category.id == category_id:
                            break
                        counter += 1
                    new_slug = test_slug
                
                update_data["slug"] = new_slug
        
        for field, value in update_data.items():
            setattr(category, field, value)
        
        category.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(category)
        
        return category
    
    def delete_category(self, category_id: int) -> bool:
        """Delete a category."""
        category = self.get_category_by_id(category_id)
        if not category:
            return False
        
        # Check if category has quotes
        if category.quote_count > 0:
            # In a real app, you might want to handle this differently
            # For now, we'll prevent deletion of categories with quotes
            return False
        
        self.db.delete(category)
        self.db.commit()
        
        return True
    
    def get_categories(
        self, 
        skip: int = 0, 
        limit: int = 100,
        search: Optional[str] = None,
        is_active: Optional[bool] = None,
        order_by_sort: bool = True
    ) -> List[Category]:
        """Get list of categories with optional filtering."""
        query = self.db.query(Category)
        
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Category.name.ilike(search_term),
                    Category.description.ilike(search_term)
                )
            )
        
        if is_active is not None:
            query = query.filter(Category.is_active == is_active)
        
        # Apply ordering
        if order_by_sort:
            query = query.order_by(Category.sort_order, Category.name)
        else:
            query = query.order_by(Category.name)
        
        return query.offset(skip).limit(limit).all()
    
    def get_category_count(
        self,
        search: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> int:
        """Get total count of categories with optional filtering."""
        query = self.db.query(Category)
        
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Category.name.ilike(search_term),
                    Category.description.ilike(search_term)
                )
            )
        
        if is_active is not None:
            query = query.filter(Category.is_active == is_active)
        
        return query.count()
    
    def get_active_categories(self) -> List[Category]:
        """Get all active categories ordered by sort_order."""
        return self.db.query(Category).filter(
            Category.is_active == True
        ).order_by(Category.sort_order, Category.name).all()
    
    def get_popular_categories(self, limit: int = 10) -> List[Category]:
        """Get most popular categories by quote count and popularity score."""
        return self.db.query(Category).filter(
            Category.is_active == True
        ).order_by(
            desc(Category.popularity_score),
            desc(Category.quote_count)
        ).limit(limit).all()
    
    def update_category_popularity(self, category_id: int, score_change: int = 1) -> bool:
        """Update category popularity score."""
        category = self.get_category_by_id(category_id)
        if not category:
            return False
        
        category.update_popularity(score_change)
        self.db.commit()
        
        return True
    
    def reorder_categories(self, category_orders: List[dict]) -> bool:
        """
        Reorder categories based on provided list.
        category_orders should be a list of dicts with 'id' and 'sort_order' keys.
        """
        try:
            for order_data in category_orders:
                category = self.get_category_by_id(order_data['id'])
                if category:
                    category.sort_order = order_data['sort_order']
                    category.updated_at = datetime.utcnow()
            
            self.db.commit()
            return True
        
        except Exception:
            self.db.rollback()
            return False
    
    def activate_category(self, category_id: int) -> bool:
        """Activate a category."""
        category = self.get_category_by_id(category_id)
        if not category:
            return False
        
        category.is_active = True
        category.updated_at = datetime.utcnow()
        self.db.commit()
        
        return True
    
    def deactivate_category(self, category_id: int) -> bool:
        """Deactivate a category."""
        category = self.get_category_by_id(category_id)
        if not category:
            return False
        
        category.is_active = False
        category.updated_at = datetime.utcnow()
        self.db.commit()
        
        return True
