"""
Analytics service for tracking user behavior and app metrics.
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc

from app.models.analytics import Analytics, UserActivity, QuoteInteraction
from app.models.user import User
from app.models.quote import Quote
from app.schemas.analytics import (
    AnalyticsCreate, UserActivityCreate, QuoteInteractionCreate,
    AnalyticsQuery, AnalyticsResult, DashboardStats, UserStats
)


class AnalyticsService:
    """Service class for analytics operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def track_metric(
        self,
        metric_name: str,
        metric_value: float,
        metric_type: str = "counter",
        category: Optional[str] = None,
        subcategory: Optional[str] = None,
        tags: Optional[Dict[str, Any]] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Analytics:
        """Track a general metric."""
        now = datetime.utcnow()
        
        analytics_record = Analytics(
            metric_name=metric_name,
            metric_value=metric_value,
            metric_type=metric_type,
            category=category,
            subcategory=subcategory,
            tags=tags,
            timestamp=now,
            date=now.strftime("%Y-%m-%d"),
            hour=now.hour,
            user_id=user_id,
            session_id=session_id
        )
        
        self.db.add(analytics_record)
        self.db.commit()
        self.db.refresh(analytics_record)
        
        return analytics_record
    
    def track_user_activity(
        self,
        user_id: int,
        activity_type: str,
        activity_data: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        platform: Optional[str] = None,
        app_version: Optional[str] = None,
        country: Optional[str] = None,
        region: Optional[str] = None,
        city: Optional[str] = None,
        duration: Optional[int] = None,
        session_id: Optional[str] = None,
        is_new_session: bool = False
    ) -> UserActivity:
        """Track user activity."""
        activity = UserActivity(
            user_id=user_id,
            activity_type=activity_type,
            activity_data=activity_data,
            ip_address=ip_address,
            user_agent=user_agent,
            platform=platform,
            app_version=app_version,
            country=country,
            region=region,
            city=city,
            duration=duration,
            session_id=session_id,
            is_new_session=is_new_session
        )
        
        self.db.add(activity)
        self.db.commit()
        self.db.refresh(activity)
        
        # Also track as a general metric
        self.track_metric(
            metric_name=f"user_activity_{activity_type}",
            metric_value=1.0,
            category="user_activity",
            subcategory=activity_type,
            user_id=user_id,
            session_id=session_id
        )
        
        return activity
    
    def track_quote_interaction(
        self,
        user_id: int,
        quote_id: int,
        interaction_type: str,
        interaction_data: Optional[Dict[str, Any]] = None,
        value: float = 1.0,
        platform: Optional[str] = None,
        source: Optional[str] = None,
        time_spent: Optional[int] = None,
        scroll_depth: Optional[float] = None,
        session_id: Optional[str] = None
    ) -> QuoteInteraction:
        """Track quote interaction."""
        interaction = QuoteInteraction(
            user_id=user_id,
            quote_id=quote_id,
            interaction_type=interaction_type,
            interaction_data=interaction_data,
            value=value,
            platform=platform,
            source=source,
            time_spent=time_spent,
            scroll_depth=scroll_depth,
            session_id=session_id
        )
        
        self.db.add(interaction)
        self.db.commit()
        self.db.refresh(interaction)
        
        # Also track as a general metric
        self.track_metric(
            metric_name=f"quote_interaction_{interaction_type}",
            metric_value=value,
            category="quote_interaction",
            subcategory=interaction_type,
            tags={"quote_id": quote_id, "platform": platform, "source": source},
            user_id=user_id,
            session_id=session_id
        )
        
        return interaction
    
    def get_analytics_data(self, query: AnalyticsQuery) -> AnalyticsResult:
        """Get analytics data based on query parameters."""
        db_query = self.db.query(Analytics)
        
        # Apply filters
        if query.metric_name:
            db_query = db_query.filter(Analytics.metric_name == query.metric_name)
        
        if query.category:
            db_query = db_query.filter(Analytics.category == query.category)
        
        if query.subcategory:
            db_query = db_query.filter(Analytics.subcategory == query.subcategory)
        
        if query.user_id:
            db_query = db_query.filter(Analytics.user_id == query.user_id)
        
        if query.start_date:
            db_query = db_query.filter(Analytics.timestamp >= query.start_date)
        
        if query.end_date:
            db_query = db_query.filter(Analytics.timestamp <= query.end_date)
        
        # Apply grouping and aggregation
        if query.group_by:
            if query.group_by == "date":
                group_field = Analytics.date
            elif query.group_by == "hour":
                group_field = Analytics.hour
            elif query.group_by == "category":
                group_field = Analytics.category
            elif query.group_by == "user_id":
                group_field = Analytics.user_id
            else:
                group_field = Analytics.date  # Default
            
            if query.aggregation == "sum":
                agg_func = func.sum(Analytics.metric_value)
            elif query.aggregation == "avg":
                agg_func = func.avg(Analytics.metric_value)
            elif query.aggregation == "count":
                agg_func = func.count(Analytics.id)
            elif query.aggregation == "min":
                agg_func = func.min(Analytics.metric_value)
            elif query.aggregation == "max":
                agg_func = func.max(Analytics.metric_value)
            else:
                agg_func = func.sum(Analytics.metric_value)  # Default
            
            results = db_query.group_by(group_field).with_entities(
                group_field.label("group_key"),
                agg_func.label("value")
            ).all()
            
            formatted_results = [
                {"group_key": str(r.group_key), "value": float(r.value)}
                for r in results
            ]
        else:
            # No grouping, just get aggregated value
            if query.aggregation == "sum":
                value = db_query.with_entities(func.sum(Analytics.metric_value)).scalar() or 0
            elif query.aggregation == "avg":
                value = db_query.with_entities(func.avg(Analytics.metric_value)).scalar() or 0
            elif query.aggregation == "count":
                value = db_query.count()
            elif query.aggregation == "min":
                value = db_query.with_entities(func.min(Analytics.metric_value)).scalar() or 0
            elif query.aggregation == "max":
                value = db_query.with_entities(func.max(Analytics.metric_value)).scalar() or 0
            else:
                value = db_query.with_entities(func.sum(Analytics.metric_value)).scalar() or 0
            
            formatted_results = [{"value": float(value)}]
        
        return AnalyticsResult(
            metric_name=query.metric_name or "all",
            results=formatted_results,
            total_records=len(formatted_results),
            aggregation=query.aggregation,
            group_by=query.group_by
        )
    
    def get_dashboard_stats(self) -> DashboardStats:
        """Get dashboard statistics."""
        now = datetime.utcnow()
        today = now.date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        # User statistics
        total_users = self.db.query(User).count()
        active_users_today = self.db.query(UserActivity).filter(
            func.date(UserActivity.timestamp) == today
        ).distinct(UserActivity.user_id).count()
        
        active_users_week = self.db.query(UserActivity).filter(
            func.date(UserActivity.timestamp) >= week_ago
        ).distinct(UserActivity.user_id).count()
        
        active_users_month = self.db.query(UserActivity).filter(
            func.date(UserActivity.timestamp) >= month_ago
        ).distinct(UserActivity.user_id).count()
        
        # Quote statistics
        total_quotes = self.db.query(Quote).count()
        total_quote_views = self.db.query(QuoteInteraction).filter(
            QuoteInteraction.interaction_type == "view"
        ).count()
        
        total_quote_likes = self.db.query(QuoteInteraction).filter(
            QuoteInteraction.interaction_type == "like"
        ).count()
        
        total_quote_shares = self.db.query(QuoteInteraction).filter(
            QuoteInteraction.interaction_type == "share"
        ).count()
        
        total_quote_favorites = self.db.query(QuoteInteraction).filter(
            QuoteInteraction.interaction_type == "favorite"
        ).count()
        
        # Average session duration
        avg_session_duration = self.db.query(func.avg(UserActivity.duration)).filter(
            UserActivity.duration.isnot(None)
        ).scalar() or 0
        
        # Top categories (placeholder)
        top_categories = []
        
        # Recent activities
        recent_activities = self.db.query(UserActivity).order_by(
            desc(UserActivity.timestamp)
        ).limit(10).all()
        
        return DashboardStats(
            total_users=total_users,
            active_users_today=active_users_today,
            active_users_week=active_users_week,
            active_users_month=active_users_month,
            total_quotes=total_quotes,
            total_quote_views=total_quote_views,
            total_quote_likes=total_quote_likes,
            total_quote_shares=total_quote_shares,
            total_quote_favorites=total_quote_favorites,
            avg_session_duration=avg_session_duration,
            top_categories=top_categories,
            recent_activities=[activity.to_dict() for activity in recent_activities]
        )
    
    def get_user_stats(self, user_id: int) -> Optional[UserStats]:
        """Get statistics for a specific user."""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            return None
        
        # Activity statistics
        total_sessions = self.db.query(UserActivity).filter(
            and_(
                UserActivity.user_id == user_id,
                UserActivity.is_new_session == True
            )
        ).count()
        
        total_time_spent = self.db.query(func.sum(UserActivity.duration)).filter(
            and_(
                UserActivity.user_id == user_id,
                UserActivity.duration.isnot(None)
            )
        ).scalar() or 0
        
        # Quote interaction statistics
        quotes_viewed = self.db.query(QuoteInteraction).filter(
            and_(
                QuoteInteraction.user_id == user_id,
                QuoteInteraction.interaction_type == "view"
            )
        ).count()
        
        quotes_liked = self.db.query(QuoteInteraction).filter(
            and_(
                QuoteInteraction.user_id == user_id,
                QuoteInteraction.interaction_type == "like"
            )
        ).count()
        
        quotes_shared = self.db.query(QuoteInteraction).filter(
            and_(
                QuoteInteraction.user_id == user_id,
                QuoteInteraction.interaction_type == "share"
            )
        ).count()
        
        quotes_favorited = self.db.query(QuoteInteraction).filter(
            and_(
                QuoteInteraction.user_id == user_id,
                QuoteInteraction.interaction_type == "favorite"
            )
        ).count()
        
        return UserStats(
            user_id=user_id,
            total_sessions=total_sessions,
            total_time_spent=int(total_time_spent),
            quotes_viewed=quotes_viewed,
            quotes_liked=quotes_liked,
            quotes_shared=quotes_shared,
            quotes_favorited=quotes_favorited,
            current_streak=user.streak_count,
            longest_streak=user.longest_streak,
            favorite_categories=[],  # Placeholder
            most_active_day="Monday",  # Placeholder
            most_active_hour=9  # Placeholder
        )
