"""
User preference model for storing user-specific settings and preferences.
"""

from datetime import datetime, time
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Foreign<PERSON>ey, Integer, String, Time, Text
from sqlalchemy.orm import relationship

from app.core.database import Base


class UserPreference(Base):
    """User preference model for storing user-specific settings."""
    
    __tablename__ = "user_preferences"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True, index=True)
    
    # Notification preferences
    notification_enabled = Column(Boolean, default=True, nullable=False)
    notification_frequency = Column(String(20), default="daily", nullable=False)  # daily, weekly, custom
    notification_time = Column(Time, default=time(9, 0), nullable=False)  # Default 9:00 AM
    notification_days = Column(String(20), default="1,2,3,4,5,6,7", nullable=False)  # Days of week (1=Monday)
    
    # Quote preferences
    preferred_quote_length = Column(String(20), default="any", nullable=False)  # short, medium, long, any
    preferred_authors = Column(Text, nullable=True)  # Comma-separated list
    excluded_authors = Column(Text, nullable=True)  # Comma-separated list
    preferred_tags = Column(Text, nullable=True)  # Comma-separated list
    
    # Display preferences
    font_size = Column(String(20), default="medium", nullable=False)  # small, medium, large
    theme_preference = Column(String(20), default="system", nullable=False)  # light, dark, system
    background_type = Column(String(20), default="image", nullable=False)  # image, color, gradient
    background_value = Column(String(255), nullable=True)  # Image URL or color code
    
    # Gamification preferences
    gamification_enabled = Column(Boolean, default=True, nullable=False)
    streak_notifications = Column(Boolean, default=True, nullable=False)
    achievement_notifications = Column(Boolean, default=True, nullable=False)
    
    # Privacy preferences
    profile_public = Column(Boolean, default=False, nullable=False)
    share_statistics = Column(Boolean, default=True, nullable=False)
    allow_friend_requests = Column(Boolean, default=True, nullable=False)
    
    # Content preferences
    show_author = Column(Boolean, default=True, nullable=False)
    show_source = Column(Boolean, default=True, nullable=False)
    auto_mark_read = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="preferences")
    category = relationship("Category", back_populates="user_preferences")
    
    def __repr__(self) -> str:
        return f"<UserPreference(id={self.id}, user_id={self.user_id}, category_id={self.category_id})>"
    
    def get_notification_days_list(self) -> list:
        """Get notification days as a list of integers."""
        if not self.notification_days:
            return [1, 2, 3, 4, 5, 6, 7]  # All days by default
        return [int(day.strip()) for day in self.notification_days.split(",") if day.strip()]
    
    def set_notification_days_from_list(self, days: list) -> None:
        """Set notification days from a list of integers."""
        self.notification_days = ",".join(str(day) for day in days)
    
    def get_preferred_authors_list(self) -> list:
        """Get preferred authors as a list."""
        if not self.preferred_authors:
            return []
        return [author.strip() for author in self.preferred_authors.split(",") if author.strip()]
    
    def set_preferred_authors_from_list(self, authors: list) -> None:
        """Set preferred authors from a list."""
        self.preferred_authors = ", ".join(authors) if authors else None
    
    def get_excluded_authors_list(self) -> list:
        """Get excluded authors as a list."""
        if not self.excluded_authors:
            return []
        return [author.strip() for author in self.excluded_authors.split(",") if author.strip()]
    
    def set_excluded_authors_from_list(self, authors: list) -> None:
        """Set excluded authors from a list."""
        self.excluded_authors = ", ".join(authors) if authors else None
    
    def get_preferred_tags_list(self) -> list:
        """Get preferred tags as a list."""
        if not self.preferred_tags:
            return []
        return [tag.strip() for tag in self.preferred_tags.split(",") if tag.strip()]
    
    def set_preferred_tags_from_list(self, tags: list) -> None:
        """Set preferred tags from a list."""
        self.preferred_tags = ", ".join(tags) if tags else None
    
    def is_notification_day(self, day_of_week: int) -> bool:
        """Check if notifications are enabled for a specific day of week."""
        return day_of_week in self.get_notification_days_list()
    
    def to_dict(self) -> dict:
        """Convert user preference to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "category_id": self.category_id,
            "notification_enabled": self.notification_enabled,
            "notification_frequency": self.notification_frequency,
            "notification_time": self.notification_time.strftime("%H:%M") if self.notification_time else None,
            "notification_days": self.get_notification_days_list(),
            "preferred_quote_length": self.preferred_quote_length,
            "preferred_authors": self.get_preferred_authors_list(),
            "excluded_authors": self.get_excluded_authors_list(),
            "preferred_tags": self.get_preferred_tags_list(),
            "font_size": self.font_size,
            "theme_preference": self.theme_preference,
            "background_type": self.background_type,
            "background_value": self.background_value,
            "gamification_enabled": self.gamification_enabled,
            "streak_notifications": self.streak_notifications,
            "achievement_notifications": self.achievement_notifications,
            "profile_public": self.profile_public,
            "share_statistics": self.share_statistics,
            "allow_friend_requests": self.allow_friend_requests,
            "show_author": self.show_author,
            "show_source": self.show_source,
            "auto_mark_read": self.auto_mark_read,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
