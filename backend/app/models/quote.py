"""
Quote model for storing motivational quotes and their metadata.
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey, Integer, String, Text, Float
from sqlalchemy.orm import relationship

from app.core.database import Base


class Quote(Base):
    """Quote model for storing motivational quotes."""
    
    __tablename__ = "quotes"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Quote content
    text = Column(Text, nullable=False, index=True)
    author = Column(String(255), nullable=False, index=True)
    source = Column(String(255), nullable=True)  # Book, speech, etc.
    
    # Categorization
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=False, index=True)
    
    # Additional metadata
    tags = Column(String(500), nullable=True)  # Comma-separated tags
    language = Column(String(10), default="en", nullable=False)
    
    # Content properties
    length = Column(Integer, nullable=False)  # Character count
    word_count = Column(Integer, nullable=False)
    reading_time = Column(Integer, nullable=False)  # Estimated reading time in seconds
    
    # Quality and moderation
    is_approved = Column(Boolean, default=True, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    quality_score = Column(Float, default=0.0, nullable=False)
    
    # Engagement metrics
    view_count = Column(Integer, default=0, nullable=False)
    like_count = Column(Integer, default=0, nullable=False)
    share_count = Column(Integer, default=0, nullable=False)
    favorite_count = Column(Integer, default=0, nullable=False)
    
    # Source and attribution
    source_url = Column(String(500), nullable=True)
    attribution_required = Column(Boolean, default=False, nullable=False)
    copyright_info = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_shown = Column(DateTime, nullable=True)
    
    # Relationships
    category = relationship("Category", back_populates="quotes")
    interactions = relationship("QuoteInteraction", back_populates="quote", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Quote(id={self.id}, author='{self.author}', text='{self.text[:50]}...')>"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if self.text:
            self.length = len(self.text)
            self.word_count = len(self.text.split())
            self.reading_time = max(1, self.word_count // 3)  # Assume 180 WPM reading speed
    
    def increment_view_count(self) -> None:
        """Increment view count and update last shown timestamp."""
        self.view_count += 1
        self.last_shown = datetime.utcnow()
    
    def increment_like_count(self) -> None:
        """Increment like count."""
        self.like_count += 1
        self.update_quality_score()
    
    def decrement_like_count(self) -> None:
        """Decrement like count."""
        if self.like_count > 0:
            self.like_count -= 1
        self.update_quality_score()
    
    def increment_share_count(self) -> None:
        """Increment share count."""
        self.share_count += 1
        self.update_quality_score()
    
    def increment_favorite_count(self) -> None:
        """Increment favorite count."""
        self.favorite_count += 1
        self.update_quality_score()
    
    def decrement_favorite_count(self) -> None:
        """Decrement favorite count."""
        if self.favorite_count > 0:
            self.favorite_count -= 1
        self.update_quality_score()
    
    def update_quality_score(self) -> None:
        """Update quality score based on engagement metrics."""
        # Simple quality score calculation
        # Weights: likes (1), shares (2), favorites (3), views (0.1)
        self.quality_score = (
            self.like_count * 1.0 +
            self.share_count * 2.0 +
            self.favorite_count * 3.0 +
            self.view_count * 0.1
        )
    
    def get_tags_list(self) -> list:
        """Get tags as a list."""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(",") if tag.strip()]
    
    def set_tags_from_list(self, tags: list) -> None:
        """Set tags from a list."""
        self.tags = ", ".join(tags) if tags else None
    
    def is_long_quote(self) -> bool:
        """Check if this is a long quote (>200 characters)."""
        return self.length > 200
    
    def get_engagement_rate(self) -> float:
        """Calculate engagement rate (interactions / views)."""
        if self.view_count == 0:
            return 0.0
        
        total_interactions = self.like_count + self.share_count + self.favorite_count
        return (total_interactions / self.view_count) * 100
    
    def to_dict(self, include_stats: bool = False) -> dict:
        """Convert quote to dictionary."""
        data = {
            "id": self.id,
            "text": self.text,
            "author": self.author,
            "source": self.source,
            "category_id": self.category_id,
            "tags": self.get_tags_list(),
            "language": self.language,
            "length": self.length,
            "word_count": self.word_count,
            "reading_time": self.reading_time,
            "is_approved": self.is_approved,
            "is_featured": self.is_featured,
            "quality_score": self.quality_score,
            "source_url": self.source_url,
            "attribution_required": self.attribution_required,
            "copyright_info": self.copyright_info,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_shown": self.last_shown.isoformat() if self.last_shown else None,
        }
        
        if include_stats:
            data.update({
                "view_count": self.view_count,
                "like_count": self.like_count,
                "share_count": self.share_count,
                "favorite_count": self.favorite_count,
                "engagement_rate": self.get_engagement_rate(),
            })
        
        return data
