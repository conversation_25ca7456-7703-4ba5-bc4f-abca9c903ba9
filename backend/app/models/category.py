"""
Category model for organizing quotes by themes and topics.
"""

from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String, Text
from sqlalchemy.orm import relationship

from app.core.database import Base


class Category(Base):
    """Category model for organizing quotes by themes."""
    
    __tablename__ = "categories"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Category information
    name = Column(String(100), unique=True, index=True, nullable=False)
    slug = Column(String(100), unique=True, index=True, nullable=False)
    description = Column(Text, nullable=True)
    
    # Display properties
    color = Column(String(7), nullable=True)  # Hex color code
    icon = Column(String(50), nullable=True)  # Icon name or emoji
    image_url = Column(String(500), nullable=True)  # Category image
    
    # Status and ordering
    is_active = Column(Boolean, default=True, nullable=False)
    sort_order = Column(Integer, default=0, nullable=False)
    
    # Metadata
    quote_count = Column(Integer, default=0, nullable=False)
    popularity_score = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    quotes = relationship("Quote", back_populates="category", cascade="all, delete-orphan")
    user_preferences = relationship("UserPreference", back_populates="category")
    
    def __repr__(self) -> str:
        return f"<Category(id={self.id}, name='{self.name}', slug='{self.slug}')>"
    
    def increment_quote_count(self) -> None:
        """Increment the quote count for this category."""
        self.quote_count += 1
    
    def decrement_quote_count(self) -> None:
        """Decrement the quote count for this category."""
        if self.quote_count > 0:
            self.quote_count -= 1
    
    def update_popularity(self, score_change: int = 1) -> None:
        """Update popularity score."""
        self.popularity_score += score_change
        if self.popularity_score < 0:
            self.popularity_score = 0
    
    def to_dict(self) -> dict:
        """Convert category to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "slug": self.slug,
            "description": self.description,
            "color": self.color,
            "icon": self.icon,
            "image_url": self.image_url,
            "is_active": self.is_active,
            "sort_order": self.sort_order,
            "quote_count": self.quote_count,
            "popularity_score": self.popularity_score,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
