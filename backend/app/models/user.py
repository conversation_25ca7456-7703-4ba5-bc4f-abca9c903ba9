"""
User model for authentication and user management.
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import <PERSON>olean, Column, DateTime, Integer, String, Text
from sqlalchemy.orm import relationship

from app.core.database import Base


class User(Base):
    """User model for storing user information and authentication."""
    
    __tablename__ = "users"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Authentication fields
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # Profile information
    full_name = Column(String(255), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    
    # Account status
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = Column(DateTime, nullable=True)
    
    # Verification and password reset
    verification_token = Column(String(255), nullable=True)
    verification_token_expires = Column(DateTime, nullable=True)
    password_reset_token = Column(String(255), nullable=True)
    password_reset_expires = Column(DateTime, nullable=True)
    
    # User preferences and settings
    timezone = Column(String(50), default="UTC", nullable=False)
    language = Column(String(10), default="en", nullable=False)
    theme_preference = Column(String(20), default="system", nullable=False)  # light, dark, system
    
    # Notification settings
    email_notifications = Column(Boolean, default=True, nullable=False)
    push_notifications = Column(Boolean, default=True, nullable=False)
    marketing_emails = Column(Boolean, default=False, nullable=False)
    
    # Gamification
    points = Column(Integer, default=0, nullable=False)
    level = Column(Integer, default=1, nullable=False)
    streak_count = Column(Integer, default=0, nullable=False)
    longest_streak = Column(Integer, default=0, nullable=False)
    last_activity_date = Column(DateTime, nullable=True)
    
    # Relationships
    preferences = relationship("UserPreference", back_populates="user", cascade="all, delete-orphan")
    activities = relationship("UserActivity", back_populates="user", cascade="all, delete-orphan")
    quote_interactions = relationship("QuoteInteraction", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    @property
    def is_authenticated(self) -> bool:
        """Check if user is authenticated (active and verified)."""
        return self.is_active and self.is_verified
    
    def update_last_login(self) -> None:
        """Update the last login timestamp."""
        self.last_login = datetime.utcnow()
    
    def update_activity(self) -> None:
        """Update last activity and handle streak logic."""
        now = datetime.utcnow()
        
        # Check if this is a new day
        if self.last_activity_date:
            days_diff = (now.date() - self.last_activity_date.date()).days
            
            if days_diff == 1:
                # Consecutive day - increment streak
                self.streak_count += 1
                if self.streak_count > self.longest_streak:
                    self.longest_streak = self.streak_count
            elif days_diff > 1:
                # Streak broken - reset
                self.streak_count = 1
            # Same day - no change to streak
        else:
            # First activity
            self.streak_count = 1
        
        self.last_activity_date = now
    
    def add_points(self, points: int) -> None:
        """Add points and update level if necessary."""
        self.points += points
        
        # Simple level calculation (every 100 points = 1 level)
        new_level = (self.points // 100) + 1
        if new_level > self.level:
            self.level = new_level
    
    def to_dict(self) -> dict:
        """Convert user to dictionary (excluding sensitive data)."""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "full_name": self.full_name,
            "avatar_url": self.avatar_url,
            "bio": self.bio,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "timezone": self.timezone,
            "language": self.language,
            "theme_preference": self.theme_preference,
            "email_notifications": self.email_notifications,
            "push_notifications": self.push_notifications,
            "marketing_emails": self.marketing_emails,
            "points": self.points,
            "level": self.level,
            "streak_count": self.streak_count,
            "longest_streak": self.longest_streak,
            "last_activity_date": self.last_activity_date.isoformat() if self.last_activity_date else None,
        }
