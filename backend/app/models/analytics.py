"""
Analytics models for tracking user behavior and app usage.
"""

from datetime import datetime
from sqlalchemy import <PERSON><PERSON>an, Column, DateTime, ForeignKey, Integer, String, Text, Float, JSON
from sqlalchemy.orm import relationship

from app.core.database import Base


class Analytics(Base):
    """General analytics model for tracking app-wide metrics."""
    
    __tablename__ = "analytics"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Metric information
    metric_name = Column(String(100), nullable=False, index=True)
    metric_value = Column(Float, nullable=False)
    metric_type = Column(String(50), nullable=False)  # counter, gauge, histogram
    
    # Dimensions
    category = Column(String(100), nullable=True, index=True)
    subcategory = Column(String(100), nullable=True)
    tags = Column(JSON, nullable=True)  # Additional metadata as JSON
    
    # Time information
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    date = Column(String(10), nullable=False, index=True)  # YYYY-MM-DD format
    hour = Column(Integer, nullable=False, index=True)  # 0-23
    
    # Context
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    session_id = Column(String(255), nullable=True, index=True)
    
    def __repr__(self) -> str:
        return f"<Analytics(id={self.id}, metric='{self.metric_name}', value={self.metric_value})>"
    
    def to_dict(self) -> dict:
        """Convert analytics record to dictionary."""
        return {
            "id": self.id,
            "metric_name": self.metric_name,
            "metric_value": self.metric_value,
            "metric_type": self.metric_type,
            "category": self.category,
            "subcategory": self.subcategory,
            "tags": self.tags,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "date": self.date,
            "hour": self.hour,
            "user_id": self.user_id,
            "session_id": self.session_id,
        }


class UserActivity(Base):
    """User activity tracking model."""
    
    __tablename__ = "user_activities"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign key
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # Activity information
    activity_type = Column(String(50), nullable=False, index=True)  # login, quote_view, share, etc.
    activity_data = Column(JSON, nullable=True)  # Additional activity data
    
    # Context
    ip_address = Column(String(45), nullable=True)  # IPv4 or IPv6
    user_agent = Column(Text, nullable=True)
    platform = Column(String(50), nullable=True)  # web, ios, android
    app_version = Column(String(20), nullable=True)
    
    # Location (if available)
    country = Column(String(2), nullable=True)  # ISO country code
    region = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)
    
    # Timing
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    duration = Column(Integer, nullable=True)  # Duration in seconds
    
    # Session tracking
    session_id = Column(String(255), nullable=True, index=True)
    is_new_session = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="activities")
    
    def __repr__(self) -> str:
        return f"<UserActivity(id={self.id}, user_id={self.user_id}, type='{self.activity_type}')>"
    
    def to_dict(self) -> dict:
        """Convert user activity to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "activity_type": self.activity_type,
            "activity_data": self.activity_data,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "platform": self.platform,
            "app_version": self.app_version,
            "country": self.country,
            "region": self.region,
            "city": self.city,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "duration": self.duration,
            "session_id": self.session_id,
            "is_new_session": self.is_new_session,
        }


class QuoteInteraction(Base):
    """Quote interaction tracking model."""
    
    __tablename__ = "quote_interactions"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    quote_id = Column(Integer, ForeignKey("quotes.id"), nullable=False, index=True)
    
    # Interaction type
    interaction_type = Column(String(50), nullable=False, index=True)  # view, like, share, favorite, copy
    
    # Interaction details
    interaction_data = Column(JSON, nullable=True)  # Additional interaction data
    value = Column(Float, default=1.0, nullable=False)  # Interaction weight/value
    
    # Context
    platform = Column(String(50), nullable=True)  # web, ios, android
    source = Column(String(100), nullable=True)  # home, search, category, random
    
    # Timing
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    session_id = Column(String(255), nullable=True, index=True)
    
    # Engagement metrics
    time_spent = Column(Integer, nullable=True)  # Time spent viewing in seconds
    scroll_depth = Column(Float, nullable=True)  # Scroll depth percentage
    
    # Relationships
    user = relationship("User", back_populates="quote_interactions")
    quote = relationship("Quote", back_populates="interactions")
    
    def __repr__(self) -> str:
        return f"<QuoteInteraction(id={self.id}, user_id={self.user_id}, quote_id={self.quote_id}, type='{self.interaction_type}')>"
    
    def to_dict(self) -> dict:
        """Convert quote interaction to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "quote_id": self.quote_id,
            "interaction_type": self.interaction_type,
            "interaction_data": self.interaction_data,
            "value": self.value,
            "platform": self.platform,
            "source": self.source,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "session_id": self.session_id,
            "time_spent": self.time_spent,
            "scroll_depth": self.scroll_depth,
        }
